#!/usr/bin/env python
"""
清理Silk性能分析数据的脚本
用于清理SQLite数据库中的silk相关表数据，减少数据库文件大小
"""

import os
import sys
import django
from django.db import connection, transaction

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CareCenter.settings')
django.setup()

def get_table_info():
    """获取silk相关表的信息"""
    with connection.cursor() as cursor:
        # 获取silk表的记录数
        tables = ['silk_request', 'silk_response', 'silk_sqlquery', 'silk_profile', 'silk_profile_queries']
        
        print("=== Silk表数据统计 ===")
        total_records = 0
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{table}: {count:,} 条记录")
            total_records += count
        
        print(f"总计: {total_records:,} 条记录")
        print()

def cleanup_silk_data():
    """清理silk数据"""
    print("=== 开始清理Silk数据 ===")
    
    with transaction.atomic():
        with connection.cursor() as cursor:
            # 清理silk相关表
            tables = ['silk_profile_queries', 'silk_profile', 'silk_sqlquery', 'silk_response', 'silk_request']
            
            for table in tables:
                print(f"清理表: {table}")
                cursor.execute(f"DELETE FROM {table}")
                affected_rows = cursor.rowcount
                print(f"删除了 {affected_rows:,} 条记录")
            
            print("所有Silk数据已清理完成")

def vacuum_database():
    """压缩数据库，回收空间"""
    print("\n=== 开始压缩数据库 ===")
    
    with connection.cursor() as cursor:
        # 获取压缩前的数据库大小
        cursor.execute("PRAGMA page_count")
        page_count_before = cursor.fetchone()[0]
        cursor.execute("PRAGMA page_size")
        page_size = cursor.fetchone()[0]
        size_before = page_count_before * page_size
        
        print(f"压缩前数据库大小: {size_before / (1024*1024):.2f} MB")
        
        # 执行VACUUM命令
        print("正在执行VACUUM命令...")
        cursor.execute("VACUUM")
        
        # 获取压缩后的数据库大小
        cursor.execute("PRAGMA page_count")
        page_count_after = cursor.fetchone()[0]
        size_after = page_count_after * page_size
        
        print(f"压缩后数据库大小: {size_after / (1024*1024):.2f} MB")
        print(f"节省空间: {(size_before - size_after) / (1024*1024):.2f} MB")
        print(f"压缩比例: {((size_before - size_after) / size_before * 100):.1f}%")

def main():
    """主函数"""
    print("SQLite数据库Silk数据清理工具")
    print("=" * 50)
    
    # 显示清理前的信息
    get_table_info()
    
    # 确认是否继续
    response = input("是否继续清理Silk数据? (y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    try:
        # 清理数据
        cleanup_silk_data()
        
        # 显示清理后的信息
        print("\n=== 清理后统计 ===")
        get_table_info()
        
        # 压缩数据库
        vacuum_database()
        
        print("\n✅ 数据库清理和压缩完成!")
        print("\n建议:")
        print("1. 在生产环境中考虑禁用Silk或设置数据保留策略")
        print("2. 定期清理Silk数据以避免数据库过大")
        print("3. 可以在settings.py中设置SILKY_MAX_RECORDED_REQUESTS限制记录数量")
        
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
