import random
from django.shortcuts import render
from rest_framework.views import APIView
from django.views import View
from core.resp import make_response
from core.wechat.captcha import verify_captcha_request
from maternity_center.models import MaternityCenter
from message.core import get_sms_client
from user.models import Staff
from .exceptions import SMSNetworkException, SMSBusinessException
from .models import SendSMSCodeLog



# 后台发送验证码
class SendStaffSMSCodeView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        cid = request.data.get('cid')
            
        mc = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not mc:
            return make_response(code=-1, msg="月子中心不存在")
        
        phone = request.data.get('phone')
        
        if not phone:
            return make_response(code=-1, msg="手机号不能为空")
        
        staff = Staff.get_staff_by_phone(phone,cid)
        
        if not staff:
            return make_response(code=-1, msg="账号不存在")
        
        lot_number = request.data.get('lot_number','')
        captcha_output = request.data.get('captcha_output','')
        pass_token = request.data.get('pass_token','')
        gen_time = request.data.get('gen_time','')
        
        if not verify_captcha_request(lot_number,pass_token,gen_time,captcha_output):
            return make_response(code=-1, msg="验证失败")


        code = str(random.randint(100000, 999999))

        try:
            client = get_sms_client()
            result = client.send_verification_code(phone, code)
            SendSMSCodeLog.send_sms_code_log(phone, code, True, result,mc)
            return make_response(code=0, msg="验证码已发送，有效期5分钟")

        except SMSNetworkException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg="短信服务网络异常")
            
        except SMSBusinessException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg=e.message)
            
        except Exception as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, str(e),mc)
            return make_response(code=-1, msg="服务器内部错误")



# 发送短信验证码
class SendSMSCodeView(APIView):

    def post(self, request):
        
        cid = request.data.get('cid')
            
        mc = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not mc:
            return make_response(code=-1, msg="月子中心不存在")
        
        phone = request.data.get('phone')
        
        if not phone:
            return make_response(code=-1, msg="手机号不能为空")
        
        lot_number = request.data.get('lot_number','')
        captcha_output = request.data.get('captcha_output','')
        pass_token = request.data.get('pass_token','')
        gen_time = request.data.get('gen_time','')
        
        if not verify_captcha_request(lot_number,pass_token,gen_time,captcha_output):
            return make_response(code=-1, msg="验证失败")


        code = str(random.randint(100000, 999999))

        try:
            client = get_sms_client()
            result = client.send_verification_code(phone, code)
            SendSMSCodeLog.send_sms_code_log(phone, code, True, result,mc)
            return make_response(code=0, msg="验证码已发送，有效期5分钟")

        except SMSNetworkException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg="短信服务网络异常")
            
        except SMSBusinessException as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, e.message,mc)
            return make_response(code=-1, msg=e.message)
            
        except Exception as e:
            SendSMSCodeLog.send_sms_code_log(phone, code, False, str(e),mc)
            return make_response(code=-1, msg="服务器内部错误")
        


# 发送审核消息
class SendAuditMessageView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        client = get_sms_client()
        result = client.send_sms_with_template('SMS_491440298','***********', {
            "application_name":"换房申请",
            "audit_result":"被拒绝",
            "audit_time":"2025-07-29 17:00"
        })
        print(result)
        return make_response(code=0, msg="审核消息已发送")


# 发送预约参观审核消息
class SendVisitAuditMessageView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        client = get_sms_client()
        result = client.send_sms_with_template('SMS_491480473','***********', {
            "brand_name":"裕康月子中心",
            "request_time":"2025-07-29 17:00",
            "application_name":"预约参观申请",
            "audit_result":"通过"
        })
        print(result)
        return make_response(code=0, msg="审核消息已发送")

# 文件无法访问
class FileErrorView(View):

    def get(self, request):
        return render(request, 'errors/file_404.html', status=404)
    