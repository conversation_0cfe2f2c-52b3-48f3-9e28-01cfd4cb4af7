from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.resp import make_response
from core.view import PaginationListBaseView
from core.wx_view import WxPaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from organizational_management.charge.enum import PackageStatusEnum, PaymentStatusEnum
from organizational_management.charge.models import MaternityCostInfo, MaternityRenewCostInfo, Package
from organizational_management.charge.serializers import BillDetailSerializer, BillListSerializer, BillUpdateSerializer, PackageSerializer, PackageUpdateSerializer, RenewCostInfoListSerializer, RenewCostInfoUpdateSerializer
from organizational_management.charge.utils import get_overview_data
from permissions.enum import PermissionEnum


def get_package_str(package):
    return f"[{package.name}({package.price}元/{package.stay_days}天)] - {package.rid}"

def get_bill_str(bill):
    return f"[{bill.maternity_admission.maternity.name})] - {bill.bid}"

def get_renew_cost_info_str(renew_info):
    return f"[{renew_info.maternity_cost_info.maternity_admission.maternity.name}({renew_info.renew_cost}元/{renew_info.renew_days}天)] - {renew_info.rbid}"


# 套餐列表
class PackageListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.PACKAGE_PRICE_VIEW
    
    serializer_class = PackageSerializer
    response_msg = "获取套餐列表成功"
    error_response_msg = ""
    search_fields = ["name"]
    audit_log_message = "套餐"
    
    def get_queryset(self):
        
        status = self.request.query_params.get('status', None)
        
        base_queryset = Package.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        if status:
            if status not in PackageStatusEnum.values:
                self.error_response_msg = "套餐状态不正确"
                return None
            base_queryset = base_queryset.filter(status=status)
        
        return base_queryset

# 套餐创建
class PackageCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.PACKAGE_PRICE_EDIT
    
    def post(self, request):
        try:
            price = request.data.get("price", 0)
            stay_days = request.data.get("stay_days", 28)
            name = request.data.get("name")
            description = request.data.get("description", "")
            
            if not name:
                return make_response(code=-1, msg="套餐名称不能为空")
            if not price:
                return make_response(code=-1, msg="套餐价格不能为空")
            if not stay_days:
                return make_response(code=-1, msg="套餐天数不能为空")
            
            if price <= 0:
                return make_response(code=-1, msg="套餐价格错误，价格不能小于等于0")
            if stay_days <= 0 or stay_days > 365:
                return make_response(code=-1, msg="套餐天数错误，天数不能小于等于0或大于365")
            
            package = Package.create_package(request.user.maternity_center, name, description, price, stay_days)
            serializer = PackageSerializer(package)
            
            AuditLogCreator.create_create_audit_log(request,"套餐",f"创建了{get_package_str(package)}<套餐>")
            
            return make_response(code=0, msg="套餐创建成功", data=serializer.data)
        except Exception as e:
            return make_response(code=-1, msg=f"套餐创建失败: {str(e)}")


# 套餐更新
class PackageUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.PACKAGE_PRICE_EDIT
    
    def put(self, request, rid):
        try:
            package = Package.objects.get(rid=rid, maternity_center=request.user.maternity_center)
            serializer = PackageUpdateSerializer(package, data=request.data)
            if serializer.is_valid():
                
                cfs = get_field_changes(package,serializer.validated_data)
                
                serializer.save()
                                
                AuditLogCreator.create_update_audit_log(request,"套餐",f"更新了{get_package_str(package)}<套餐>",cfs)
                
                return make_response(code=0, msg="套餐更新成功", data=serializer.data)
            return make_response(code=-1, msg=serializer.errors)
        except Package.DoesNotExist:
            return make_response(code=-1, msg="套餐不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"套餐更新失败: {str(e)}")

# 套餐删除
class PackageDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.PACKAGE_PRICE_EDIT
    
    def delete(self, request, rid):
        try:
            package = Package.objects.get(rid=rid, maternity_center=request.user.maternity_center)
            package.delete()
            AuditLogCreator.create_delete_audit_log(request,"套餐",f"删除了{get_package_str(package)}<套餐>")
            return make_response(code=0, msg="套餐删除成功")
        except Package.DoesNotExist:
            return make_response(code=-1, msg="套餐不存在")
        except Exception as e:
            return make_response(code=-1, msg=f"套餐删除失败: {str(e)}")
        
        


# 结算单列表    
class BillListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.BILL_VIEW
    
    serializer_class = BillListSerializer
    response_msg = "获取结算单列表成功"
    error_response_msg = ""
    search_fields = ["maternity_admission__maternity__name","maternity_admission__room__room_number","bid"]
    audit_log_message = "结算单"
    
    def get_queryset(self):
        
        base_queryset = MaternityCostInfo.objects.select_related('maternity_admission__maternity','maternity_admission__room','package').filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')

        status = self.request.query_params.get('status', None)
        
        
        if status:
            if status not in PaymentStatusEnum.values:
                self.error_response_msg = "支付状态不正确"
                return None
            base_queryset = base_queryset.filter(payment_status=status)
            
        return base_queryset


# 结算单详情
class BillDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.BILL_VIEW

    def get(self, request, bid):
        
        bill = MaternityCostInfo.get_maternity_cost_info_by_bid(bid,request.user.maternity_center)
        
        if not bill:
            return make_response(code=-1, msg="结算单不存在")
        
        serializer = BillDetailSerializer(bill)
        
        AuditLogCreator.create_query_audit_log(request, "结算单", f"查看了{get_bill_str(bill)}<结算单>详情")
        return make_response(code=0, msg="获取结算单详情成功", data=serializer.data)


# 结算单更新
class BillUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.BILL_EDIT
    
    def put(self, request, bid):
        
        bill = MaternityCostInfo.get_maternity_cost_info_by_bid(bid,request.user.maternity_center)
        
        if not bill:
            return make_response(code=-1, msg="结算单不存在")
        
        serializer = BillUpdateSerializer(bill, data=request.data,context={'request':request})
        
        if serializer.is_valid():
            cfs = get_field_changes(bill, serializer.validated_data)
            serializer.save()
            
            AuditLogCreator.create_update_audit_log(request, "结算单", f"更新了{get_bill_str(bill)}<结算单>", cfs)
            return make_response(code=0, msg="结算单更新成功", data=serializer.data)
        
        return make_response(code=-1, msg=serializer.errors)
    
    
    

# 续住费用信息列表
class RenewCostInfoListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.BILL_VIEW
    
    serializer_class = RenewCostInfoListSerializer
    response_msg = "获取续住费用信息列表成功"
    error_response_msg = ""
    audit_log_message = "续住费用信息"
    
    def get_queryset(self):
        
        base_queryset = MaternityRenewCostInfo.objects.filter(maternity_cost_info__maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        return base_queryset
    

# 续住费用信息更新
class RenewCostInfoUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.BILL_EDIT
    
    def put(self, request, rbid):
        
        renew_cost_info = MaternityRenewCostInfo.get_maternity_renew_cost_info_by_rbid(rbid,self.request.user.maternity_center)
        
        if not renew_cost_info:
            return make_response(code=-1, msg="续住费用信息不存在")
        
        serializer = RenewCostInfoUpdateSerializer(renew_cost_info, data=request.data)
        
        if serializer.is_valid():
            cfs = get_field_changes(renew_cost_info, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "续住费用信息", f"更新了{get_renew_cost_info_str(renew_cost_info)}<续住费用信息>", cfs)
            return make_response(code=0, msg="续住费用信息更新成功", data=serializer.data)
        
        return make_response(code=-1, msg=serializer.errors)
    
    
# 总览
class OverviewView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.SUPER_ADMIN
    
    def get(self, request):
        
        AuditLogCreator.create_query_audit_log(request, "费用总览", "查看了<费用总览>")
        return get_overview_data(request)