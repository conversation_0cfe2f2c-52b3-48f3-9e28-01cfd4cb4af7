from rest_framework import serializers
from django.conf import settings
from rest_framework.response import Serializer
from core.parse_time import ShanghaiFriendlyDateTimeField, parse_datetime_to_shanghai_time
from file.models import StaffAssessmentRecordAttachment, StaffContractFile, StaffHealthCheckRecordAttachment, StaffQualificationCertificateFile, StaffTrainingRecordAttachment
from .enum import TrainingResultEnum
from .models import HandoverReport, Schedule, StaffAssessmentRecord, StaffContract, \
    StaffHealthCheckRecord, StaffQualificationCertificate, StaffTrainingRecord


# 排班序列化器
class ScheduleSerializer(serializers.ModelSerializer):
    # 员工姓名
    staff_name = serializers.SerializerMethodField()
    # 班次显示
    shift_type_display = serializers.SerializerMethodField()
    # 状态显示
    status_display = serializers.SerializerMethodField()
    # 员工sid
    staff_sid = serializers.SerializerMethodField()
    staff_phone = serializers.CharField(source='staff.phone', read_only=True)
    department_rid = serializers.CharField(source='staff.department.rid', read_only=True)
    department_name = serializers.CharField(source='staff.department.name', read_only=True)

    class Meta:
        model = Schedule
        fields = ['rid', 'staff_sid', 'staff_name', 'staff_phone', 'department_rid', 'department_name', 'position', 'schedule_date',
                 'shift_type', 'shift_type_display', 'status', 'status_display', 'remark']

    def get_staff_name(self, obj):
        return obj.staff.name if obj.staff else '-'
    
    def get_staff_sid(self, obj):
        return obj.staff.sid if obj.staff else '-'

    def get_shift_type_display(self, obj):
        # 班次类型中文映射（兼容多种格式）
        SHIFT_TYPE_MAPPING = {
            'MORNING': '早班',
            'AFTERNOON': '中班',
            'NIGHT': '晚班',
            # 兼容旧的命名
            'DAY_SHIFT': '早班',
            'NIGHT_SHIFT': '夜班',
            'EVENING_SHIFT': '晚班',
        }
        return SHIFT_TYPE_MAPPING.get(obj.shift_type, obj.shift_type)

    def get_status_display(self, obj):
        return obj.get_status_display()

# 交班报告列表序列化器
class HandoverReportListSerializer(serializers.ModelSerializer):
    # 排班班次
    schedule_shift_type = serializers.CharField(source='schedule.shift_type')
    # 排班班次显示
    schedule_shift_type_display = serializers.SerializerMethodField()
    # 排班日期
    schedule_date = serializers.DateField(source='schedule.schedule_date')
    # 员工部门
    staff_department_name = serializers.CharField(source='schedule.staff.department.name')
    # 员工姓名
    staff_name = serializers.CharField(source='schedule.staff.name')
    # 交班时间
    shift_time = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = HandoverReport
        fields = ['rid', 'schedule_shift_type', 'schedule_shift_type_display', 'schedule_date', 'staff_department_name', 'staff_name',
                  'shift_time', 'work_summary']

    def get_schedule_shift_type_display(self, obj):
        return obj.schedule.get_shift_type_display()


class ScheduleHandoverReportSerializer(serializers.ModelSerializer):
    # 排班rid
    schedule_rid = serializers.CharField(source='rid')
    # 排班班次
    schedule_shift_type = serializers.CharField(source='shift_type')
    # 排班班次显示
    schedule_shift_type_display = serializers.SerializerMethodField()
    # 交班报告rid
    rid = serializers.SerializerMethodField()
    # 交班时间
    shift_time = serializers.SerializerMethodField()
    # 工作总结
    work_summary = serializers.SerializerMethodField()
    # 特殊情况跟进
    special_situation_follow_up = serializers.SerializerMethodField()
    # 设备异常情况
    equipment_abnormal_situation = serializers.SerializerMethodField()
    # 交接事项
    item_handover = serializers.SerializerMethodField()
    # 其他重要通知
    other_important_notice = serializers.SerializerMethodField()
    # 未完成工作 
    unfinished_work = serializers.SerializerMethodField()
    # 创建时间
    created_at = serializers.SerializerMethodField()
    # 更新时间
    updated_at = serializers.SerializerMethodField()

    class Meta:
        model = Schedule
        fields = ['schedule_rid', 'schedule_shift_type', 'schedule_shift_type_display', 'schedule_date', 'rid',
                  'shift_time', 'work_summary', 'special_situation_follow_up', 'equipment_abnormal_situation',
                  'item_handover', 'other_important_notice', 'unfinished_work', 'created_at', 'updated_at']

    def get_schedule_shift_type_display(self, obj):
        # 班次类型中文映射
        return obj.get_shift_type_display()

    def get_handover_report(self, obj):
        return obj.handover_reports.first()

    def get_rid(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.rid if handover_report else None

    def get_shift_time(self, obj):
        handover_report = self.get_handover_report(obj)
        return parse_datetime_to_shanghai_time(handover_report.shift_time) if handover_report else None

    def get_work_summary(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.work_summary if handover_report else None

    def get_special_situation_follow_up(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.special_situation_follow_up if handover_report else None

    def get_equipment_abnormal_situation(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.equipment_abnormal_situation if handover_report else None

    def get_item_handover(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.item_handover if handover_report else None

    def get_other_important_notice(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.other_important_notice if handover_report else None

    def get_unfinished_work(self, obj):
        handover_report = self.get_handover_report(obj)
        return handover_report.unfinished_work if handover_report else None

    def get_created_at(self, obj):
        handover_report = self.get_handover_report(obj)
        return parse_datetime_to_shanghai_time(handover_report.created_at) if handover_report else None

    def get_updated_at(self, obj):
        handover_report = self.get_handover_report(obj)
        return parse_datetime_to_shanghai_time(handover_report.updated_at) if handover_report else None

    def to_representation(self, instance):
        """重写序列化表示方法，根据是否有交班报告来动态返回字段"""
        data = super().to_representation(instance)
        
        # 如果没有交班报告，则移除交班报告相关字段
        handover_report = self.get_handover_report(instance)
        if not handover_report:
            handover_fields = [
                'rid', 'shift_time', 'work_summary', 'special_situation_follow_up',
                'equipment_abnormal_situation', 'item_handover', 'other_important_notice',
                'unfinished_work', 'created_at', 'updated_at'
            ]
            for field in handover_fields:
                data.pop(field, None)
        
        return data

# 交班报告详情序列化器
class HandoverReportDetailSerializer(serializers.ModelSerializer):
    # 排班rid
    schedule_rid = serializers.CharField(source='schedule.rid')
    # 排班班次
    schedule_shift_type = serializers.CharField(source='schedule.shift_type')
    # 排班班次显示
    schedule_shift_type_display = serializers.SerializerMethodField()
    # 排班日期
    schedule_date = serializers.DateField(source='schedule.schedule_date')
    # 员工部门
    staff_department_name = serializers.CharField(source='schedule.staff.department.name')
    # 员工姓名
    staff_name = serializers.CharField(source='schedule.staff.name')
    # 交班时间
    shift_time = ShanghaiFriendlyDateTimeField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = HandoverReport
        fields = ['rid', 'schedule_rid', 'schedule_shift_type', 'schedule_shift_type_display', 'schedule_date', 'staff_department_name', 'staff_name',
                  'shift_time', 'work_summary','special_situation_follow_up', 'equipment_abnormal_situation',
                  'item_handover', 'other_important_notice', 'unfinished_work', 'created_at', 'updated_at']
    
    def get_schedule_shift_type_display(self, obj):
        return obj.schedule.get_shift_type_display()

# 交班报告创建序列化器
class HandoverReportCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = HandoverReport
        fields = ['schedule', 'shift_time', 'work_summary', 'special_situation_follow_up', 'equipment_abnormal_situation',
                  'item_handover', 'other_important_notice', 'unfinished_work']

# 交班报告更新序列化器
class HandoverReportUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = HandoverReport
        fields = ['shift_time', 'work_summary', 'special_situation_follow_up', 'equipment_abnormal_situation',
                  'item_handover', 'other_important_notice', 'unfinished_work']


# 员工合同详情序列化器
class StaffContractDetailSerializer(serializers.ModelSerializer):
    
    # 文件url
    file_url = serializers.SerializerMethodField()
    # file
    file = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = StaffContract
        fields = ['rid',  'contract_number', 'contract_validity_period', 'file','file_url', 'created_at', 'updated_at','file','creator_name']
    
    def get_file_url(self, obj):
        if obj.file and obj.file.file:
            return obj.file.file.url
        else:
            return settings.FILE_NOT_FOUND_URL
        
    def get_file(self, obj):
        return obj.file.rid
    
    def get_creator_name(self, obj):
        print(obj.creator)
        return obj.creator.name if obj.creator else '-'
    
# 员工合同创建
class StaffContractCreateSerializer(serializers.ModelSerializer):
    
    file = serializers.CharField()

    class Meta:
        model = StaffContract
        fields = ['maternity_center','staff', 'contract_number', 'contract_validity_period', 'file','creator']

    def validate_file(self, value):
        if not value:
            raise serializers.ValidationError("文件不能为空")
        sc = StaffContractFile.get_by_rid(value,self.context['request'].user.maternity_center)
        
        if not sc:
            raise serializers.ValidationError("文件不存在")
        
        return sc
    
    

# 员工资质证书详情序列化器
class StaffQualificationCertificateDetailSerializer(serializers.ModelSerializer):
    
    # 文件url
    file_url = serializers.SerializerMethodField()
    # file
    file = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 创建人
    creator_name = serializers.SerializerMethodField()
    
    class Meta:
        model = StaffQualificationCertificate
        fields = ['rid',  'file', 'file_url', 'issuing_authority', 'certificate_name', 'validity_period','created_at', 'updated_at','creator_name']
        
    def get_file_url(self, obj):
        if obj.file and obj.file.file:
            return obj.file.file.url
        else:
            return settings.FILE_NOT_FOUND_URL

    def get_file(self, obj):
        return obj.file.rid
     
    def get_creator_name(self, obj):
        print(obj.creator)
        return obj.creator.name if obj.creator else '-'
# 员工资质证书创建序列化器
class StaffQualificationCertificateCreateSerializer(serializers.ModelSerializer):

    file = serializers.CharField()
    
    class Meta:
        model = StaffQualificationCertificate
        fields = ['maternity_center', 'staff', 'file', 'issuing_authority', 'certificate_name', 'validity_period', 'creator']

    def validate_file(self, value):
        if not value:
            raise serializers.ValidationError("文件不能为空")
        sqc = StaffQualificationCertificateFile.get_by_rid(value,self.context['request'].user.maternity_center)
        
        if not sqc:
            raise serializers.ValidationError("文件不存在")
        
        return sqc        

# 员工培训记录详情序列化器
class StaffTrainingRecordDetailSerializer(serializers.ModelSerializer):
    training_result_label = serializers.SerializerMethodField()
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    attachment_urls = serializers.SerializerMethodField()

    class Meta:
        model = StaffTrainingRecord
        fields = ['rid', 'training_topics', 'training_date', 'training_duration', 'training_result', 'training_result_label', 'attachment', 'attachment_urls', 'created_at', 'updated_at']


    def get_training_result_label(self, obj):
        return TrainingResultEnum(obj.training_result).label if obj.training_result else '-'
    
    def get_attachment_urls(self, obj):
        urls = []
        for rid in obj.attachment:
            file = StaffTrainingRecordAttachment.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
    
    
# 员工培训创建序列化器
class StaffTrainingRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffTrainingRecord
        fields = ['staff', 'training_topics', 'training_date', 'training_duration', 'training_result', 'attachment']

        
    def validate_training_result(self, value):
        if value not in [TrainingResultEnum.PASS, TrainingResultEnum.FAIL, TrainingResultEnum.ABSENT]:
            raise serializers.ValidationError("培训结果必须为合格、不合格或缺勤")
        return value
    



# 员工培训更新序列化器
class StaffTrainingRecordUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffTrainingRecord
        fields = ['training_topics', 'training_date', 'training_duration', 'training_result', 'attachment']

    def validate_training_result(self, value):
        if value not in [TrainingResultEnum.PASS, TrainingResultEnum.FAIL, TrainingResultEnum.ABSENT]:
            raise serializers.ValidationError("培训结果必须为合格、不合格或缺勤")
        return value






# 员工考核记录详情序列化器
class StaffAssessmentRecordDetailSerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    attachment_urls = serializers.SerializerMethodField()

    class Meta:
        model = StaffAssessmentRecord
        fields = ['rid', 'assessment_name', 'assessment_date', 'assessment_result', 'assessment_comment', 'attachment','attachment_urls', 'created_at', 'updated_at']


    
    def get_attachment_urls(self, obj):
        urls = []
        for rid in obj.attachment:
            file = StaffAssessmentRecordAttachment.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
    
    
# 员工考核创建序列化器
class StaffAssessmentRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffAssessmentRecord
        fields = ['staff', 'assessment_name', 'assessment_date', 'assessment_result', 'assessment_comment', 'attachment']

    def validate_staff_number(self, value):
        from user.models import Staff
        try:
            Staff.objects.get(staff_number=value)
            return value
        except Staff.DoesNotExist:
            raise serializers.ValidationError(f"工号为 '{value}' 的员工不存在")
    

    

# 员工考核更新序列化器
class StaffAssessmentRecordUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = StaffAssessmentRecord
        fields = ['assessment_name', 'assessment_date', 'assessment_result', 'assessment_comment', 'attachment']
        
        
        
        

# 员工健康检查记录详情序列化器
class StaffHealthCheckRecordDetailSerializer(serializers.ModelSerializer):
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    attachment_urls = serializers.SerializerMethodField()
    class Meta:
        model = StaffHealthCheckRecord
        fields = ['rid', 'health_check_project', 'health_check_date', 'health_check_result', 'attachment', 'attachment_urls', 'created_at', 'updated_at']
    
    def get_attachment_urls(self, obj):
        urls = []
        for rid in obj.attachment:
            file = StaffHealthCheckRecordAttachment.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
    
# 员工健康检查创建序列化器
class StaffHealthCheckRecordCreateSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffHealthCheckRecord
        fields = ['staff', 'health_check_project', 'health_check_date', 'health_check_result', 'attachment']

    def validate_staff_number(self, value):
        from user.models import Staff
        try:
            Staff.objects.get(staff_number=value)
            return value
        except Staff.DoesNotExist:
            raise serializers.ValidationError(f"工号为 '{value}' 的员工不存在")
    


    

# 员工健康检查更新序列化器
class StaffHealthCheckRecordUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = StaffHealthCheckRecord
        fields = ['health_check_project', 'health_check_date', 'health_check_result', 'attachment']