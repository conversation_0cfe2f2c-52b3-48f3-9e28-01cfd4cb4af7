from dirtyfields import DirtyFieldsMixin
from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from maternity_center.models import MaternityCenter
from user.models import Staff
from .enum import ReportStatusEnum


# 院感制度文档
class InfectionDocument(BaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 标题  
    name = models.CharField(max_length=100, verbose_name="标题")
    # 版本号
    version = models.CharField(max_length=100, verbose_name="版本号")
    # 发布日期
    publish_date = models.DateField(verbose_name="发布日期")
    # 描述
    description = models.TextField(verbose_name="描述")
    # 文件
    file = models.CharField(max_length=500, verbose_name="文件")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "院感制度文档"
        verbose_name_plural = "院感制度文档"
        
    def __str__(self):
        return self.name
    

    @classmethod
    def get_infection_document_by_rid(cls,rid,maternity_center):
        try:    
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    
    @classmethod
    def check_duplicate_name(cls,name,maternity_center,exclude_rid=None):
        if exclude_rid:
            return cls.objects.filter(name=name,maternity_center=maternity_center).exclude(rid=exclude_rid).exists()
        else:
            return cls.objects.filter(name=name,maternity_center=maternity_center).exists()
        
        

# 感染检测与报告
class InfectionDetectionAndReport(BaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True)
    # 监测日期
    detection_date = models.DateField(verbose_name="监测日期")
    # 监测内容
    detection_content = models.TextField(verbose_name="监测内容")
    # 发现问题
    problem = models.TextField(verbose_name="发现问题")
    # 上报情况
    report_status = models.CharField(max_length=30, verbose_name="上报情况", choices=ReportStatusEnum.choices)
    # 处理结果
    processing_result = models.TextField(verbose_name="处理结果")
    # 详细描述
    detailed_description = models.TextField(verbose_name="详细描述")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="创建人", blank=True, null=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "感染检测与报告"
        verbose_name_plural = "感染检测与报告"
    
    def __str__(self):
        return self.detection_date
    
    @classmethod
    def get_infection_detection_and_report_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
        
    
    