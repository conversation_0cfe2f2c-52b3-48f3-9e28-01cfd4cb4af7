from dirtyfields import DirtyFieldsMixin
from django.db import models

from core.generate_hashid import generate_questionnaire_code, generate_resource_uuid
from core.model import BaseModel
from maternity_center.models import MaternityCenter
from user.models import Maternity, Staff
from .enum import QuestionTypeEnum, QuestionnaireAvailableStageEnum, QuestionnaireTypeEnum


class Questionnaire(BaseModel, DirtyFieldsMixin):
    """问卷模型"""
    title = models.CharField(max_length=255, verbose_name='问卷标题')
    description = models.TextField(blank=True, null=True, verbose_name='问卷描述')
    questionnaire_type = models.CharField(max_length=20, choices=QuestionnaireTypeEnum.choices, verbose_name='问卷类型')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    # 可见阶段
    available_stage = models.CharField(
        max_length=20, 
        choices=QuestionnaireAvailableStageEnum.choices,
        default=QuestionnaireAvailableStageEnum.GENERAL,
        verbose_name='可见阶段'
    )
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, related_name='questionnaires', verbose_name='所属中心')
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='created_questionnaires', verbose_name='创建人')
    qid = models.CharField(max_length=100, verbose_name='问卷编号', default=generate_questionnaire_code)

    class Meta:
        verbose_name = '问卷'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    @classmethod
    def get_all_questionnaire_types(cls, maternity_center=None):
        if not maternity_center:
            return []
        return list(set(cls.objects.filter(maternity_center=maternity_center).values_list('questionnaire_type', flat=True)))
    def __str__(self):
        return self.title

class Question(models.Model):
    """问题模型"""
    questionnaire = models.ForeignKey(Questionnaire, on_delete=models.CASCADE, related_name='questions', verbose_name='所属问卷')
    content = models.TextField(verbose_name='问题内容')
    question_type = models.CharField(max_length=20, choices=QuestionTypeEnum.choices, verbose_name='问题类型')
    is_required = models.BooleanField(default=True, verbose_name='是否必填')
    order = models.PositiveIntegerField(default=0, verbose_name='问题顺序')
    rid = models.CharField(max_length=100, verbose_name='问题编号', default=generate_resource_uuid)

    class Meta:
        verbose_name = '问题'
        verbose_name_plural = verbose_name
        ordering = ['order']

    def __str__(self):
        return f"{self.questionnaire.title} - {self.content[:50]}"

class Choice(models.Model):
    """选项模型"""
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='choices', verbose_name='所属问题')
    content = models.CharField(max_length=255, verbose_name='选项内容')
    order = models.PositiveIntegerField(default=0, verbose_name='选项顺序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    rid = models.CharField(max_length=100, verbose_name='选项编号', default=generate_resource_uuid)

    class Meta:
        verbose_name = '选项'
        verbose_name_plural = verbose_name
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.question.content[:30]} - {self.content}"

class QuestionnaireResponse(models.Model, DirtyFieldsMixin):
    """问卷回复模型"""
    questionnaire = models.ForeignKey(Questionnaire, on_delete=models.CASCADE, related_name='responses', verbose_name='问卷')
    customer = models.ForeignKey(Maternity, on_delete=models.CASCADE, related_name='questionnaire_responses', verbose_name='回答者')
    submitted_at = models.DateTimeField(auto_now_add=True, verbose_name='提交时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    rid = models.CharField(max_length=100, verbose_name='问卷回复编号', default=generate_resource_uuid)

    class Meta:
        verbose_name = '问卷回复'
        verbose_name_plural = verbose_name
        ordering = ['-submitted_at']

    def __str__(self):
        return f"{self.customer.name} - {self.questionnaire.title}"

class Answer(models.Model):
    """答案模型"""
    response = models.ForeignKey(QuestionnaireResponse, on_delete=models.CASCADE, related_name='answers', verbose_name='问卷回复')
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='answers', verbose_name='问题')
    text_answer = models.TextField(null=True, blank=True, verbose_name='文本答案')
    boolean_answer = models.BooleanField(null=True, blank=True, verbose_name='布尔答案')
    half_star_count = models.IntegerField(null=True, blank=True, verbose_name='半星数量')
    selected_choice = models.ForeignKey(Choice, on_delete=models.SET_NULL, null=True, blank=True, related_name='single_answers', verbose_name='单选答案')
    selected_choices = models.ManyToManyField(Choice, blank=True, related_name='multiple_answers', verbose_name='多选答案')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    rid = models.CharField(max_length=100, verbose_name='答案编号', default=generate_resource_uuid)

    class Meta:
        verbose_name = '答案'
        verbose_name_plural = verbose_name
        ordering = ['created_at']

    def __str__(self):
        return f"{self.question.content[:30]} - {self.response.customer.name}"
