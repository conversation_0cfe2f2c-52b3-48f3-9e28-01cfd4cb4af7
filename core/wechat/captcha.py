


import hmac
import json

import requests


def verify_captcha_request(lot_number,pass_token,gen_time,captcha_output):

    captcha_id = "5b96fef3516676f28fb14b1088c37bd4"
    captcha_key = "b55e6a59b3b77f6f71904e6a98cbf1f2"
    api_server = "https://captcha.alicaptcha.com"


    lotnumber_bytes = lot_number.encode()
    prikey_bytes = captcha_key.encode()
    sign_token = hmac.new(prikey_bytes, lotnumber_bytes, digestmod="SHA256").hexdigest()

    query = {
        "lot_number": lot_number,
        "captcha_output": captcha_output,
        "pass_token": pass_token,
        "gen_time": gen_time,
        "sign_token": sign_token,
    }

    url = api_server + "/validate" + "?captcha_id={}".format(captcha_id)

    try:
        res = requests.post(url, data=query)
        assert res.status_code == 200
        msg = json.loads(res.text)
    except Exception as e:
        msg = {"result": "error", "reason": "request api fail",'error_trace':str(e)}
    
    if msg.get('result') == "success":
        return True
    
    else:
        return False