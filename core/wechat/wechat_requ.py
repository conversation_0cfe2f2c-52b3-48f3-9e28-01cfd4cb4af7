import json
import requests
from core.wechat.wechat_token_manager import WechatAccessTokenManager


def wx_request_user_phone(code):
    
    access_token = WechatAccessTokenManager().get_access_token()
    info = WechatAccessTokenManager().get_token_info()
    
    print(f'info: {info}')
    
    if not access_token:
        return -1, '微信鉴权失败，请稍后再试！'

        
    url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber"
    
    params = {
        'access_token': access_token,
    }
    
    post_data = {
        'code': code
    }
    
    try:
        response = requests.post(url, params=params, json=post_data, timeout=10)
        
        response.raise_for_status()
        
        data = response.json()
        
        
        if 'phone_info' in data and data['phone_info']['purePhoneNumber'] != "":
            
            if  data['phone_info']['countryCode'] != '86':
                return -1, '暂不支持国外运营商'
            
            return 0, data['phone_info']['purePhoneNumber']
        else:
            error_msg = data.get('errmsg', '未知错误')
            error_code = data.get('errcode', -1)
            print(f"微信API返回错误: code={error_code}, msg={error_msg}")
            return -1, "授权失败，请稍后再试！"
            
    except requests.exceptions.RequestException as e:
        print(f"请求微信API网络异常: {e}")
        return -1, "网络异常，请稍后再试！"
    except json.JSONDecodeError as e:
        print(f"解析微信API响应JSON异常: {e}")
        return -1, "数据解析异常，请稍后再试！"
    except Exception as e:
        print(f"请求微信API未知异常: {e}")
        return -1, "未知异常，请稍后再试！"
    
    
    