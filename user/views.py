from datetime import timed<PERSON>ta

from django.utils import timezone
from rest_framework.views import APIView

from audit_log.enum import OperationTypeEnum
from audit_log.models import AuditLog
from core.authorization import CareCenterAuthentication, HasStaffPermission, StaffWithSpecificPermissionOnly, \
    create_user_token, get_user_model
from core.logs import AuditLog<PERSON><PERSON>
from core.model import get_field_changes
from core.resp import make_response
from core.view import PaginationListBaseView
from core.wechat.wechat_requ import wx_request_user_phone
from maternity_center.models import Maternity<PERSON>enter
from message.models import SendSMSCodeLog
from permissions.enum import PermissionEnum
from permissions.serializers import StaffRoleSerializer
from user.models import Maternity
from user.serializers import MaternityCreateSerializer, MaternityUpdateSerializer, MaternitySerializer
from user.utils import check_maternity_duplicates


# 登录视图
class StaffLoginView(APIView):
    
    authentication_classes = []
    permission_classes = []
    
    def post(self, request):
        phone = request.data.get("phone")
        password = request.data.get("password")
        user_type = request.data.get("user_type", "staff")
        cid = request.data.get("cid", None)
        user_model, user_serializer = get_user_model(user_type)
        
        if not cid:
            return make_response(code=-1, msg="请选择月子中心")
        
        if not user_model:
            return make_response(code=-1, msg="无效的登录凭据")
        
        try:
            user = user_model.objects.get(phone=phone,maternity_center__cid=cid)
        except user_model.DoesNotExist:
            return make_response(code=-1, msg="无效的登录凭据")
        
        if user.check_password(password):
            user.update_last_login()
            resp = {
                "token": create_user_token(user.uid, user_type),
                "user": user_serializer(user).data,
            }
            
            if user_type == 'staff':
                request.user = user
                AuditLogCreator.create_login_audit_log(self.request, "用户登录", f"{user.name} 于 {timezone.now().strftime('%Y-%m-%d %H:%M:%S')} 登录后台")
            
            return make_response(resp)
        else:
            return make_response(code=-1, msg="无效的登录凭据")
        
class PermissionView(APIView):

    #角色与权限列表,仅员工可以访问

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]

    def get(self, request):
        data = StaffRoleSerializer(request.user.role).data
        return make_response(data)


# 验证短信验证码
class VerifyCodeView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        phone = request.data.get("phone")
        code = request.data.get("code")
        cid = request.data.get("cid")
        user_type = request.data.get("user_type", "staff")
        
        maternity_center = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not maternity_center:
            return make_response(code=-1, msg="月子中心不存在")
        
        if not phone or not code:
            return make_response(code=-1, msg="数据校验失败")
        
        try:
            sms_code = SendSMSCodeLog.objects.get(phone=phone, code=code,is_used=False,maternity_center__cid=cid)
        except SendSMSCodeLog.DoesNotExist:
            return make_response(code=-1, msg="验证码错误")
        
        
        if sms_code.send_time < timezone.now() - timedelta(minutes=5):
            sms_code.mark_as_failed()
            return make_response(code=-1, msg="验证码已过期，请重新发送")
        
        sms_code.mark_as_verified()
        
        user_model, user_serializer = get_user_model(user_type)
        
        
        if not user_model:
            return make_response(code=-1, msg="无效的登录类型")
        
                
        try:
            user = user_model.objects.get(phone=phone,maternity_center=maternity_center)
            user.update_last_login()
            resp = {
                "token": create_user_token(user.uid, user_type),
                "user": user_serializer(user).data,
            }
            return make_response(resp)
        except user_model.DoesNotExist:
            if user_type == 'maternity':
                user = user_model.objects.create(phone=phone,name=f"产妇{phone}",maternity_center=maternity_center)
                user.update_last_login()
                resp = {
                    "token": create_user_token(user.uid, user_type),
                    "user": user_serializer(user).data,
                }
                return make_response(resp)
            else:
                sms_code.mark_as_failed()
                return make_response(code=-1, msg="用户不存在")
                
def get_maternity_str(maternity):
    return f"[{maternity.name}({maternity.phone})] - {maternity.uid}"

# 产妇列表
class MaternityListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNITY_USER_VIEW
    serializer_class = MaternitySerializer
    response_msg = "产妇列表获取成功"
    error_response_msg = "获取产妇列表失败"
    search_fields = ['name','phone','identity_number']
    audit_log_message = "产妇"
    
    def get_queryset(self):
        return Maternity.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')    



# 创建产妇
class MaternityCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNITY_USER_EDIT

    def post(self, request):

        data = request.data.copy()

        phone = data.get("phone")
        identity_number = data.get("identity_number")

        if not phone or not identity_number:
            return make_response(code=-1, msg=f"电话号码和身份证号不能为空")

        res = check_maternity_duplicates(phone, identity_number, request.user.maternity_center)
        if res:
            return res
        
        data['maternity_center'] = request.user.maternity_center.id

        try:
            serializer = MaternityCreateSerializer(data=data)
            if serializer.is_valid():
                serializer.save()
            else:
                print(serializer.errors)
                return make_response(code=-1, msg="数据校验失败，请检查录入数据是否正确")
            AuditLogCreator.create_create_audit_log(request, "产妇", f"创建了{get_maternity_str(serializer.instance)}<产妇>")
            return make_response(code=0, msg="产妇创建成功",data=MaternitySerializer(serializer.instance).data)
        except Exception :
            return make_response(code=-1, msg="产妇创建失败，请稍后重试")

# 更新产妇
class MaternityUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNITY_USER_EDIT
    
    def put(self, request, uid):
        
        maternity = Maternity.get_maternity_by_uid(uid,request.user.maternity_center)
        if not maternity:
            return make_response(code=-1, msg="产妇不存在")

        data = request.data.copy()
        
        phone = data.get("phone","")
        identity_number = data.get("identity_number","")

        
        res = check_maternity_duplicates(phone, identity_number, request.user.maternity_center,exclude_id=maternity.id)
        if res:
            return res

        try:
            serializer = MaternityUpdateSerializer(maternity,data=data,partial=True)
            if serializer.is_valid():
                cfs = get_field_changes(maternity,data)
                serializer.save()
            else:
                print(serializer.errors)
                return make_response(code=-1, msg="数据校验失败，请检查录入数据是否正确")
            AuditLogCreator.create_update_audit_log(request, "产妇", f"更新了{get_maternity_str(maternity)}<产妇>", cfs)
            return make_response(code=0, msg="产妇信息更新成功",data=MaternitySerializer(serializer.instance).data)
        except Exception :
            return make_response(code=-1, msg="产妇信息更新失败，请稍后重试")


# 微信授权手机号码交换
class WechatAuthView(APIView):

    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        code = request.data.get('code',None)
        user_type = request.data.get('user_type', None)
        cid = request.data.get('cid', None)
        
        if not code or not user_type or not cid:
            return make_response(code=-1, msg="数据校验失败")
        
        maternity_center = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not maternity_center:
            return make_response(code=-1, msg="月子中心不存在")

        status_code, result = wx_request_user_phone(code)
        
        if status_code != 0:
            return make_response(code=-1, msg=result)
        
        
        user_model, user_serializer = get_user_model(user_type)
        
        
        if not user_model:
            return make_response(code=-1, msg="无效的登录类型")
        
                
        try:
            user = user_model.objects.get(phone=result,maternity_center=maternity_center)
            
            user.update_last_login()
            
            resp = {
                "token": create_user_token(user.uid, user_type),
                "user": user_serializer(user).data,
            }
            return make_response(resp)
        
        except user_model.DoesNotExist:
            if user_type == 'maternity':
                user = user_model.objects.create(phone=result,name=f"Q{result}",maternity_center=maternity_center)
                user.update_last_login()
                resp = {
                    "token": create_user_token(user.uid, user_type),
                    "user": user_serializer(user).data,
                }
                return make_response(resp)
            else:
                return make_response(code=-1, msg="用户不存在")

# 微信验证短信验证码
class WechatVerifyCodeView(APIView):
    
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        phone = request.data.get("phone")
        code = request.data.get("code")
        cid = request.data.get("cid")
        user_type = request.data.get("user_type", "maternity")
        
        maternity_center = MaternityCenter.get_maternity_center_by_cid(cid)
        
        if not maternity_center:
            return make_response(code=-1, msg="月子中心不存在")
        
        if not phone or not code:
            return make_response(code=-1, msg="数据校验失败")
        
        try:
            sms_code = SendSMSCodeLog.objects.get(phone=phone, code=code,is_used=False,maternity_center__cid=cid)
        except SendSMSCodeLog.DoesNotExist:
            return make_response(code=-1, msg="验证码错误")
        
        
        if sms_code.send_time < timezone.now() - timedelta(minutes=5):
            sms_code.mark_as_failed()
            return make_response(code=-1, msg="验证码已过期，请重新发送")
        
        sms_code.mark_as_verified()
        
        user_model, user_serializer = get_user_model(user_type)
        
        
        if not user_model:
            return make_response(code=-1, msg="无效的登录类型")
        
                
        try:
            user = user_model.objects.get(phone=phone,maternity_center=maternity_center)
            user.update_last_login()
            resp = {
                "token": create_user_token(user.uid, user_type),
                "user": user_serializer(user).data,
            }
            return make_response(resp)
        except user_model.DoesNotExist:
            if user_type == 'maternity':
                user = user_model.objects.create(phone=phone,name=f"产妇{phone}",maternity_center=maternity_center)
                user.update_last_login()
                resp = {
                    "token": create_user_token(user.uid, user_type),
                    "user": user_serializer(user).data,
                }
                return make_response(resp)
            else:
                sms_code.mark_as_failed()
                return make_response(code=-1, msg="用户不存在")