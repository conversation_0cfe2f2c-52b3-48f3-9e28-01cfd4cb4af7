import bcrypt
from dirtyfields import DirtyFieldsMixin
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone

from core.enum import GenderEnum, BloodTypeEnum
from core.generate_hashid import generate_resource_uuid, generate_staff_code, generate_user_uid
from core.model import BaseModel
from hospital.models import Hospital, HospitalDepartment
from maternity_center.models import Maternity<PERSON>enter
from permissions.models import StaffRole
from user.enum import EmergencyContactEnum


# 部门
class Department(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", blank=True, null=True, related_name="departments")
    # 部门名称
    name = models.CharField(max_length=100, verbose_name="部门名称")
    # rid
    rid = models.CharField(max_length=100, verbose_name="rid", default=generate_resource_uuid)

    class Meta:
        verbose_name = "部门"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name
    
    @classmethod
    def get_department_by_rid(cls, rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None


# 用户基础模型
class UserBaseModel(BaseModel):
    # 用户id
    uid = models.CharField(max_length=30, unique=True,default=generate_user_uid, editable=False)
    # 姓名
    name = models.CharField(max_length=100, verbose_name="姓名",blank=True, default='')
    # 电话
    phone = models.CharField(max_length=11, verbose_name="电话",unique=True,)
    # 出生日期
    birth_date = models.DateField(verbose_name="出生日期", blank=True, null=True)
    # 民族
    ethnicity = models.CharField(max_length=50, verbose_name="民族",blank=True, default='')
    # 籍贯
    native_place = models.CharField(max_length=100, verbose_name="籍贯",blank=True, default='')
    # 性别
    gender = models.IntegerField(verbose_name="性别", choices=GenderEnum.choices, blank=True, null=True)
    # 血型
    blood_type = models.CharField(max_length=10, choices=BloodTypeEnum.choices, verbose_name="血型",blank=True, default=BloodTypeEnum.UNKNOWN)
    # 密码
    password = models.CharField(max_length=128, verbose_name="密码",blank=True, default='')
    # 家庭住址
    home_address = models.TextField(verbose_name="家庭地址", blank=True, default='')
    # 最后登录时间
    last_login = models.DateTimeField(verbose_name="最后登录时间", blank=True, null=True)
    # 是否启用
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    
    # 不创建表
    class Meta:
        abstract = True

    # 更新最后登录时间  
    def update_last_login(self):
        self.last_login = timezone.now()
        self.save()

    # 设置密码
    def set_password(self, raw_password):
        self.password = bcrypt.hashpw(
            raw_password.encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8")

    # 检查密码  
    def check_password(self, raw_password):
        return bcrypt.checkpw(
            raw_password.encode("utf-8"), self.password.encode("utf-8")
        )

# 产妇用户模型
# TODO：待完善
class Maternity(UserBaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.SET_NULL, null=True,blank=True,verbose_name="月子中心",related_name="maternity")
    # 身份证号
    identity_number = models.CharField(max_length=100, verbose_name="身份证号",blank=True, default='')
    # 紧急联系人
    emergency_contact = models.CharField(max_length=100, verbose_name="紧急联系人", blank=True, default='')
    # 紧急联系人电话
    emergency_contact_phone = models.CharField(max_length=11, verbose_name="紧急联系人电话", blank=True, default='')
    # 紧急联系人关系
    emergency_contact_relation = models.CharField(max_length=100, verbose_name="紧急联系人关系", choices=EmergencyContactEnum.choices, blank=True, default=EmergencyContactEnum.UNKNOWN)
    
    class Meta:
        verbose_name = "产妇"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=['phone', 'maternity_center'],
                name='unique_phone_per_center'
            ),
            models.UniqueConstraint(
                fields=['identity_number', 'maternity_center'],
                condition=models.Q(identity_number__gt=''),
                name='unique_identity_per_center'
            ),
        ]

        

    def __str__(self):
        return self.name
    
    @classmethod
    def get_maternity_by_uid(cls, uid,maternity_center):
        try:
            return cls.objects.get(uid=uid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    

# 员工用户模型
# TODO：待完善
class Staff(UserBaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.SET_NULL, null=True,blank=True,verbose_name="月子中心",related_name="staff")
    # 身份证号
    identity_number = models.CharField(max_length=100, verbose_name="身份证号",blank=True, default='')
    # 紧急联系人
    emergency_contact = models.CharField(max_length=100, verbose_name="紧急联系人", default='',blank=True)
    # 紧急联系人电话
    emergency_contact_phone = models.CharField(max_length=11, verbose_name="紧急联系人电话", default='',blank=True)
    # 紧急联系人关系
    emergency_contact_relation = models.CharField(max_length=100, verbose_name="紧急联系人关系", choices=EmergencyContactEnum.choices, default=EmergencyContactEnum.OTHER)
    # 工号
    staff_number = models.CharField(max_length=100,  verbose_name="工号", blank=True, null=True,default='')
    # 部门
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, verbose_name="部门", blank=True, null=True, related_name="staffs")
    # 职位
    position = models.CharField(max_length=100, verbose_name="职位", blank=True, null=True, default='')
    # 角色
    role = models.ForeignKey(StaffRole, on_delete=models.CASCADE, verbose_name="角色", blank=True, null=True, related_name="staffs")
    # 入职日期
    hire_date = models.DateField(verbose_name="入职日期", blank=True, null=True)
    # 合同号
    contract_number = models.CharField(max_length=100, verbose_name="合同号", blank=True, null=True,default='')
    # 合同有效期
    contract_validity_date = models.DateField(verbose_name="合同有效期", blank=True, null=True)
    # 员工编号
    sid = models.CharField(max_length=100,  unique=True,verbose_name="员工编号", blank=True, default=generate_staff_code,editable=False)
    
    class Meta:
        verbose_name = "内部员工"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.name}"
    
    def has_permission(self, permission):
        # 检查用户是否拥有指定权限
        if self.role:
            has_perm = self.role.has_permission(permission)
            print(f"DEBUG: Staff.has_permission - Has permission: {has_perm}")
            return has_perm
        print("DEBUG: Staff.has_permission - No role assigned")
        return False
    
    @classmethod
    def get_staff_by_sid(cls, sid,maternity_center):
        try:
            return cls.objects.get(sid=sid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
        
    @classmethod
    def get_staff_by_phone(cls, phone,cid):
        try:
            return cls.objects.select_related('maternity_center').get(phone=phone,maternity_center__cid=cid)
        except cls.DoesNotExist:
            return None

