from datetime import datetime

from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import ApprovalStatusEnum
from core.logs import AuditLog<PERSON>reator
from core.model import get_field_changes
from core.parse_time import parse_datetime_string
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.outing_management.enum import OutingStatusEnum
from customer_service.outing_management.models import OutingApplication
from customer_service.outing_management.serializers import OutingApplicationCreateSerializer, \
    OutingApplicationDetailSerializer, OutingApplicationSerializer, OutingApplicationUpdateSerializer
from permissions.enum import PermissionEnum
from user.models import Staff


class OutingApplicationBaseView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    
    
def get_outing_application_str(outing_application):
    maternity_name = outing_application.maternity_admission.maternity.name
    room_number = outing_application.maternity_admission.room.room_number if outing_application.maternity_admission.room else '-'
    return f"[{maternity_name}({room_number})] - {outing_application.oid}"

class OutingApplicationListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.OUT_VIEW
    
    serializer_class = OutingApplicationSerializer
    response_msg = "获取外出申请列表成功"
    error_response_msg = ""
    search_fields = ["maternity_admission__maternity__name","maternity_admission__room__room_number"]
    
    def get_queryset(self):
        
        aps = self.request.query_params.get('aps', None)
        apt = self.request.query_params.get('apt', None)
        ogs = self.request.query_params.get('ogs', None)
        
        base_queryset = OutingApplication.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-application_time')
        
        if aps:
            if aps not in ApprovalStatusEnum.values:
                self.error_response_msg = "审核状态不正确"
                return None
            base_queryset = base_queryset.filter(approval_status=aps)
            
        if ogs:
            if ogs not in OutingStatusEnum.values:
                self.error_response_msg = "外出状态不正确"
                return None
            base_queryset = base_queryset.filter(outing_status=ogs)
            
        if apt:
            try:
                apt_datetime = datetime.strptime(apt, "%Y-%m-%d %H:%M:%S")
                base_queryset = base_queryset.filter(outing_time__gte=apt_datetime)
            except ValueError:
                self.error_response_msg = "查询日期格式错误"
                return None
                
        AuditLogCreator.create_query_audit_log(self.request,"外出管理","查询了<外出>申请列表")
                
        return base_queryset

class OutingApplicationDetailView(OutingApplicationBaseView):
    
    staff_required_permission = PermissionEnum.OUT_VIEW
    
    def get(self, request, oid):
        try:
            outing_application = OutingApplication.objects.select_related('maternity_admission','maternity_admission__maternity','maternity_admission__room').get(oid=oid,maternity_center=request.user.maternity_center)
            serializer = OutingApplicationDetailSerializer(outing_application)
            
            AuditLogCreator.create_query_audit_log(request,"外出管理",f"查看了{get_outing_application_str(outing_application)}<外出>申请单")
            
            return make_response(code=0, msg="获取外出申请详情成功", data=serializer.data)
        except OutingApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")
    
class OutingApplicationCreateView(OutingApplicationBaseView):
    
    staff_required_permission = PermissionEnum.OUT_EDIT

    def post(self, request, aid):
        
        data = request.data.copy()
        
        accompany_staff = data.get("accompany_staff", None)
        need_accompany = data.get("need_accompany", False)
        
        if need_accompany:
            if not accompany_staff:
                return make_response(code=-1, msg="数据校验失败，陪同外出必须提供陪同人员")
            staff = Staff.get_staff_by_sid(accompany_staff,request.user.maternity_center)
            if not staff:
                return make_response(code=-1, msg="数据校验失败，陪同人员不存在")
            data['accompany_staff'] = staff.id
        
        try:
            maternity_admission = MaternityAdmission.objects.select_related(
                'maternity','room'
            ).get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        if OutingApplication.has_pending_outing(maternity_admission):
            return make_response(code=-1, msg="产妇有未处理的外出申请，暂无法创建新的外出申请")
        
        data["maternity_center"] = request.user.maternity_center.id
        data["maternity_admission"] = maternity_admission.id
        
        serializer = OutingApplicationCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save(maternity_admission=maternity_admission)
            resData = OutingApplicationDetailSerializer(serializer.instance).data
            
            AuditLogCreator.create_create_audit_log(request,"外出管理",f"创建了{get_outing_application_str(serializer.instance)}<外出>申请单")

            
            return make_response(code=0, msg="创建外出申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)
        
        

class OutingApplicationUpdateView(OutingApplicationBaseView):
    
    staff_required_permission = PermissionEnum.OUT_EDIT

    def put(self, request, oid):
        
        data = request.data.copy()
        
        try:
            outing_application = OutingApplication.objects.select_related('maternity_admission','maternity_admission__maternity','maternity_admission__room').get(oid=oid,maternity_center=request.user.maternity_center)
        except OutingApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")
        
        if outing_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="外出申请单已审核，无法修改")
        
        accompany_staff = data.get("accompany_staff", None)
        
        if accompany_staff:
            staff = Staff.get_staff_by_sid(accompany_staff,request.user.maternity_center)
            if not staff:
                return make_response(code=-1, msg="数据校验失败，陪同人员不存在")
            data['accompany_staff'] = staff.id
        
        
        serializer = OutingApplicationUpdateSerializer(outing_application, data=data)
        if serializer.is_valid():
            cfs = get_field_changes(outing_application,serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(
                request,"外出管理",f"更新了{get_outing_application_str(outing_application)}<外出>申请单",cfs
            )
            resData = OutingApplicationDetailSerializer(serializer.instance).data
            return make_response(code=0, msg="更新外出申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)


class OutingApplicationDeleteView(OutingApplicationBaseView):
    
    staff_required_permission = PermissionEnum.OUT_EDIT

    def delete(self, request, oid):
        try:
            outing_application = OutingApplication.objects.select_related(
                'maternity_admission__maternity','maternity_admission__room'
            ).get(oid=oid,maternity_center=request.user.maternity_center)
        except OutingApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")
        
        if outing_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="外出申请单已审核，无法删除，审核结果为：%s" % ApprovalStatusEnum(outing_application.approval_status).label)
        
        outing_application.delete()
        
        AuditLogCreator.create_delete_audit_log(
            request,"外出管理",f"删除了{get_outing_application_str(outing_application)}<外出>申请单"
        )
        
        return make_response(code=0, msg="删除外出申请成功")

class OutingApplicationAuditView(OutingApplicationBaseView):
    
    staff_required_permission = PermissionEnum.OUT_AUDIT

    def put(self, request, oid):
        
        result = request.data.get("result", None)
        
        if result not in [True, False]:
            return make_response(code=-1, msg="数据校验失败，审核结果不正确或未提供审核结果")
        
        try:
            outing_application = OutingApplication.objects.select_related(
                'maternity_admission__maternity','maternity_admission__room'
            ).get(oid=oid,maternity_center=request.user.maternity_center)
        except OutingApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")
        
        if outing_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="外出申请单已审核，无法再次审核，审核结果为：%s" % ApprovalStatusEnum(outing_application.approval_status).label)
        
        if result:
            outing_application.outing_status = OutingStatusEnum.OUTING
        outing_application.approval_status = ApprovalStatusEnum.APPROVED if result else ApprovalStatusEnum.REJECTED
        outing_application.approval_time = timezone.now()
        outing_application.approval_by = request.user
        
        outing_application.save()
        resData = OutingApplicationDetailSerializer(outing_application).data
        
        AuditLogCreator.create_request_audit_log(
            request,"外出管理",f"审核了{get_outing_application_str(outing_application)}<外出>申请单,审核结果为：{ApprovalStatusEnum(outing_application.approval_status).label}"
        )
        return make_response(code=0, msg="外出申请审核成功", data=resData)
    
    
# 外出返回
class OutingApplicationReturnView(OutingApplicationBaseView):

    staff_required_permission = PermissionEnum.OUT_EDIT

    def put(self, request, oid):
        
        return_remark = request.data.get("return_remark", '')
        
        actual_return_time = request.data.get("actual_return_time", None)
        if not actual_return_time:
            return make_response(code=-1, msg="数据校验失败，实际返回时间未提供")
        actual_return_time = parse_datetime_string(actual_return_time, format_str="%Y-%m-%d %H:%M:%S")
        if not actual_return_time:
            return make_response(code=-1, msg="数据校验失败，实际返回时间格式错误")
        
        try:
            outing_application = OutingApplication.objects.select_related(
                'maternity_admission__maternity','maternity_admission__room'
            ).get(oid=oid,maternity_center=request.user.maternity_center)
        except OutingApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")

        if outing_application.approval_status != ApprovalStatusEnum.APPROVED:
            return make_response(code=-1, msg=f"外出申请单未审核通过，无法返回，审核结果为：{ApprovalStatusEnum(outing_application.approval_status).label}")

        if outing_application.outing_status != OutingStatusEnum.OUTING:
            return make_response(code=-1, msg=f"当前状态非外出中，无法执行返回操作，当前状态为：{OutingStatusEnum(outing_application.outing_status).label}")

        if outing_application.actual_return_time:
            return make_response(code=-1, msg=f"该外出申请已经返回，无法重复操作，返回时间为：{outing_application.actual_return_time}")

        # 设置实际返回时间
        outing_application.actual_return_time = actual_return_time
        outing_application.return_remark = return_remark

        # 判断是否逾期返回
        if actual_return_time <= outing_application.expected_return_time:
            outing_application.outing_status = OutingStatusEnum.RETURNED_ON_TIME
        else:
            outing_application.outing_status = OutingStatusEnum.RETURNED_OVERDUE

        outing_application.save()
        
        AuditLogCreator.create_status_change_audit_log(
            request,"外出管理",f"标记了{get_outing_application_str(outing_application)}<外出>申请单为已返回，返回备注：{return_remark}"
        )

        resData = OutingApplicationDetailSerializer(outing_application).data
        return make_response(code=0, 
                             msg=f"标记（{outing_application.maternity_admission.maternity.name}）外出返回成功，返回状态：{OutingStatusEnum(outing_application.outing_status).label}", 
                             data=resData)
