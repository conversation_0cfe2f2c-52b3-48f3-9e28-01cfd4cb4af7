from django.conf import settings
from django.utils import timezone
from rest_framework import serializers

from core.enum import ApprovalStatusEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from core.utils import calculate_age
from customer_service.medical_referral.enum import EmergencyLevelEnum, ReferralStatusEnum
from customer_service.medical_referral.models import MaternityReferralApplication, NewbornReferralApplication
from file.models import MedicalReferralFile, MedicalReferralReturnFile


# 产妇转诊列表序列化器
class MedicalReferralSerializer(serializers.ModelSerializer):
    
    emergency_level_label = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    application_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()
    maternity_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    referral_status_display = serializers.SerializerMethodField()
    class Meta:
        model = MaternityReferralApplication
        fields = ["rid","maternity_name","room_number","referral_status","referral_status_display","referral_department","main_symptoms_and_reasons",
                  "emergency_level","emergency_level_label","application_time","approval_status","approval_status_label","actual_return_time"]
        
    def get_emergency_level_label(self, obj):
        return EmergencyLevelEnum(obj.emergency_level).label
    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_room_number(self, obj):
        return obj.maternity_admission.room.room_number if obj.maternity_admission.room else "-"
    
    def get_referral_status_display(self, obj):
        return ReferralStatusEnum(obj.referral_status).label    
    
# 产妇转诊详情序列化器
class MedicalReferralDetailSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    application_time = ShanghaiFriendlyDateTimeField()
    approval_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()

    emergency_level_label = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    maternity_name = serializers.SerializerMethodField()
    maternity_age = serializers.SerializerMethodField()
    postpartum_days = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    approval_by = serializers.SerializerMethodField()
    aid = serializers.SerializerMethodField()
    referral_status_display = serializers.SerializerMethodField()
    related_inspection_information_urls = serializers.SerializerMethodField()
    referral_return_discharge_urls = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityReferralApplication
        exclude = ["maternity_center","maternity_admission","id"]

    def get_maternity_name(self, obj):
        return obj.maternity_admission.maternity.name if obj.maternity_admission.maternity else "-"
    
    def get_maternity_age(self, obj):
        if obj.maternity_admission.maternity and obj.maternity_admission.maternity.birth_date:
            return calculate_age(obj.maternity_admission.maternity.birth_date)
        else:
            return "-"
    
    def get_postpartum_days(self, obj):
        return (timezone.now().date() - obj.maternity_admission.actual_delivery_date).days if obj.maternity_admission.actual_delivery_date else "-"
    
    def get_room_number(self, obj):
        return obj.maternity_admission.room.room_number if obj.maternity_admission.room else "-"
    
    def get_emergency_level_label(self, obj):
        return EmergencyLevelEnum(obj.emergency_level).label
    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_approval_by(self, obj):
        return obj.approval_by.name if obj.approval_by else "-"

    def get_aid(self, obj):
        return obj.maternity_admission.aid if obj.maternity_admission else None
    
    def get_referral_status_display(self, obj):
        return ReferralStatusEnum(obj.referral_status).label
    
    def get_related_inspection_information_urls(self, obj):
        urls = []
        for rid in obj.related_inspection_information:
            file = MedicalReferralFile.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
    
    def get_referral_return_discharge_urls(self, obj):
        urls = []
        for rid in obj.referral_return_discharge:
            file = MedicalReferralReturnFile.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
# 产妇转诊申请创建序列化器
class MedicalReferralCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityReferralApplication
        fields = ["maternity_center","maternity_admission","referral_department",
                  "main_symptoms_and_reasons","emergency_level","vital_signs",
                  "related_medical_history","related_inspection_information",]
        
    def validate_related_inspection_information(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("相关检查资料格式错误")
        
        if value:
            rids = value
            
            existing_files = MedicalReferralFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的相关检查资料，请删除后重新上传")
            
            return value
        return value
        
        
# 产妇转诊申请更新序列化器
class MedicalReferralUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = MaternityReferralApplication
        fields = ["referral_department","main_symptoms_and_reasons","emergency_level","vital_signs",
                  "related_medical_history","related_inspection_information","actual_return_time"]

    def validate_related_inspection_information(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("相关检查资料格式错误")
        
        if value:
            rids = value
            
            existing_files = MedicalReferralFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的相关检查资料，请删除后重新上传")
            
            return value
        return value




# 新生儿转诊列表序列化器
class NewbornMedicalReferralSerializer(serializers.ModelSerializer):
    
    emergency_level_label = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    application_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()
    maternity_name = serializers.SerializerMethodField()
    baby_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    referral_status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = NewbornReferralApplication
        fields = ["rid","maternity_name","baby_name","room_number","referral_status","referral_status_display","referral_department","main_symptoms_and_reasons",
                  "emergency_level","emergency_level_label","application_time","approval_status","approval_status_label","actual_return_time"]
        
    def get_emergency_level_label(self, obj):
        return EmergencyLevelEnum(obj.emergency_level).label
    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_maternity_name(self, obj):
        return obj.baby.maternity_admission.maternity.name if obj.baby.maternity_admission.maternity else "-"
    
    def get_baby_name(self, obj):
        return obj.baby.name if obj.baby else "-"
    
    def get_room_number(self, obj):
        return obj.baby.maternity_admission.room.room_number if obj.baby.maternity_admission.room else "-"
    
    def get_referral_status_display(self, obj):
        return ReferralStatusEnum(obj.referral_status).label
    
# 新生儿转诊详情序列化器
class NewbornMedicalReferralDetailSerializer(serializers.ModelSerializer):
    
    created_at = ShanghaiFriendlyDateTimeField()
    updated_at = ShanghaiFriendlyDateTimeField()
    application_time = ShanghaiFriendlyDateTimeField()
    approval_time = ShanghaiFriendlyDateTimeField()
    actual_return_time = ShanghaiFriendlyDateTimeField()
    baby_name = serializers.SerializerMethodField()
    emergency_level_label = serializers.SerializerMethodField()
    approval_status_label = serializers.SerializerMethodField()
    maternity_name = serializers.SerializerMethodField()
    birth_days = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    approval_by = serializers.SerializerMethodField()
    aid = serializers.SerializerMethodField()
    nid = serializers.SerializerMethodField()
    referral_status_display = serializers.SerializerMethodField()
    related_inspection_information_urls = serializers.SerializerMethodField()
    referral_return_discharge_urls = serializers.SerializerMethodField()
    class Meta:
        model = NewbornReferralApplication
        exclude = ["maternity_center","baby","id"]
        
    def get_baby_name(self, obj):
        return obj.baby.name if obj.baby else "-"

    def get_maternity_name(self, obj):
        return obj.baby.maternity_admission.maternity.name if obj.baby.maternity_admission.maternity else "-"
    
    def get_birth_days(self, obj):
        return (timezone.now().date() - obj.baby.birth_time.date()).days if obj.baby.birth_time else "-"
    
    
    def get_room_number(self, obj):
        return obj.baby.maternity_admission.room.room_number if obj.baby.maternity_admission.room else "-"
    
    def get_emergency_level_label(self, obj):
        return EmergencyLevelEnum(obj.emergency_level).label
    
    def get_approval_status_label(self, obj):
        return ApprovalStatusEnum(obj.approval_status).label
    
    def get_approval_by(self, obj):
        return obj.approval_by.name if obj.approval_by else "-"

    def get_aid(self, obj):
        return obj.baby.maternity_admission.aid if obj.baby.maternity_admission else None

    def get_nid(self, obj):
        return obj.baby.nid if obj.baby else None
    
    def get_referral_status_display(self, obj):
        return ReferralStatusEnum(obj.referral_status).label
    
    def get_related_inspection_information_urls(self, obj):
        urls = []
        for rid in obj.related_inspection_information:
            file = MedicalReferralFile.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
    
    def get_referral_return_discharge_urls(self, obj):
        urls = []
        for rid in obj.referral_return_discharge:
            file = MedicalReferralReturnFile.get_by_rid(rid,obj.maternity_center)
            if file:
                urls.append(file.file.url)
            else:
                urls.append(settings.FILE_NOT_FOUND_URL)
        return urls
# 转诊申请创建序列化器
class NewbornMedicalReferralCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornReferralApplication
        fields = ["maternity_center","baby","referral_department",
                  "main_symptoms_and_reasons","emergency_level","vital_signs",
                  "related_medical_history","related_inspection_information",]
        
    def validate_related_inspection_information(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("相关检查资料格式错误")
        
        if value:
            rids = value
            
            existing_files = MedicalReferralFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的相关检查资料，请删除后重新上传")
            
            return value
        return value



# 转诊申请更新序列化器
class NewbornMedicalReferralUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = NewbornReferralApplication
        fields = ["referral_department","main_symptoms_and_reasons","emergency_level","vital_signs",
                  "related_medical_history","related_inspection_information","actual_return_time"]

    def validate_related_inspection_information(self, value):
        if not isinstance(value, list):
            raise serializers.ValidationError("相关检查资料格式错误")
        
        if value:
            rids = value
            
            existing_files = MedicalReferralFile.objects.filter(
                rid__in=rids,
                maternity_center=self.context['request'].user.maternity_center
            )
            
            existing_rids = set(existing_files.values_list('rid', flat=True))
            
            input_rids = set(rids)
            missing_rids = input_rids - existing_rids
            
            if missing_rids:
                raise serializers.ValidationError(f"有不存在的相关检查资料，请删除后重新上传")
            
            return value
        return value