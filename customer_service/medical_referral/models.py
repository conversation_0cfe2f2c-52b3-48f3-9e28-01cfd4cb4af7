from dirtyfields import DirtyFieldsMixin
from django.db import models
from django.utils import timezone

from core.enum import ApprovalStatusEnum
from core.generate_hashid import generate_maternity_referral_code, generate_newborn_referral_code
from core.model import BaseModel
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.medical_referral.enum import EmergencyLevelEnum, ReferralStatusEnum
from maternity_center.models import MaternityCenter
from user.models import Staff


# 产妇转诊申请单
class MaternityReferralApplication(DirtyFieldsMixin,BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='maternity_referral_applications')
    # 入住单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入住单",related_name='medical_referral_applications')
    # 转诊科室
    referral_department = models.CharField(max_length=20, verbose_name="转诊科室",blank=True,default='')
    # 主要症状/原因:
    main_symptoms_and_reasons = models.TextField(verbose_name="主要症状/原因",blank=True,default='')
    # 紧急程度
    emergency_level = models.CharField(max_length=20, verbose_name="紧急程度", choices=EmergencyLevelEnum.choices,default=EmergencyLevelEnum.GENERAL)
    # 生命体征
    vital_signs = models.TextField(verbose_name="生命体征",blank=True,default='')
    # 相关病史
    related_medical_history = models.TextField(verbose_name="相关病史",blank=True,default='')
    # 相关检查资料
    related_inspection_information = models.JSONField(verbose_name="相关检查资料", blank=True, default=list)    
    # 转诊状态
    referral_status = models.CharField(max_length=20, verbose_name="转诊状态", choices=ReferralStatusEnum.choices,default=ReferralStatusEnum.PENDING_AUDIT)
    # 转诊单编号
    rid = models.CharField(max_length=100, verbose_name="产妇转诊单编号",unique=True,blank=True,default=generate_maternity_referral_code)
    # 申请时间
    application_time = models.DateTimeField(verbose_name="申请时间",auto_now_add=True)
    # 审核状态
    approval_status = models.CharField(max_length=20, verbose_name="审核状态", choices=ApprovalStatusEnum.choices,default=ApprovalStatusEnum.PENDING)
    # 审核意见
    approval_opinion = models.TextField(verbose_name="审核意见",blank=True,default='')
    # 审核时间
    approval_time = models.DateTimeField(verbose_name="审核时间",blank=True,null=True)
    # 审核人
    approval_by = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="审核人",blank=True,null=True)
    # 实际返回时间
    actual_return_time = models.DateTimeField(verbose_name="实际返回时间",blank=True,null=True)
    # 转诊返回出院小结（文件）
    referral_return_discharge = models.JSONField(verbose_name="转诊返回出院小结",blank=True,default=list)
    
    class Meta:
        verbose_name = "产妇院内转诊申请单"
        verbose_name_plural = "产妇院内转诊申请单"
        
    def __str__(self):
        return f"{self.maternity_admission.maternity}的产妇院内转诊申请"
    
    # 查询产妇是否有未处理的转诊单
    @classmethod
    def has_pending_referral(cls,maternity_admission):
        return cls.objects.filter(maternity_admission=maternity_admission, approval_status=ApprovalStatusEnum.PENDING).exists()
    
    # 审核通过转诊申请
    def approve_referral(self, staff,opinion):
        self.approval_status = ApprovalStatusEnum.APPROVED 
        self.referral_status = ReferralStatusEnum.PENDING_REFERRAL
        self.approval_opinion = opinion
        self.approval_time = timezone.now()
        self.approval_by = staff
        self.save()
        return self
    
    # 拒绝转诊申请
    def reject_referral(self, staff, opinion):
        self.approval_status = ApprovalStatusEnum.REJECTED
        self.referral_status = ReferralStatusEnum.REJECTED
        self.approval_opinion = opinion
        self.approval_time = timezone.now()
        self.approval_by = staff
        self.save()
        return self
        

# 新生儿转诊申请单
class NewbornReferralApplication(DirtyFieldsMixin,BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='newborn_referral_applications')
    # 新生儿
    baby = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿",related_name='medical_referral_applications')
    # 转诊科室
    referral_department = models.CharField(max_length=20, verbose_name="转诊科室",blank=True,default='')
    # 主要症状/原因:
    main_symptoms_and_reasons = models.TextField(verbose_name="主要症状/原因",blank=True,default='')
    # 紧急程度
    emergency_level = models.CharField(max_length=20, verbose_name="紧急程度", choices=EmergencyLevelEnum.choices,default=EmergencyLevelEnum.GENERAL)
    # 生命体征
    vital_signs = models.TextField(verbose_name="生命体征",blank=True,default='')
    # 相关病史
    related_medical_history = models.TextField(verbose_name="相关病史",blank=True,default='')
    # 相关检查资料
    related_inspection_information = models.JSONField(verbose_name="相关检查资料", blank=True, default=list)    
    # 转诊状态
    referral_status = models.CharField(max_length=20, verbose_name="转诊状态", choices=ReferralStatusEnum.choices,default=ReferralStatusEnum.PENDING_AUDIT)
    # 转诊单编号
    rid = models.CharField(max_length=100, verbose_name="新生儿转诊单编号",unique=True,blank=True,default=generate_newborn_referral_code)
    # 申请时间
    application_time = models.DateTimeField(verbose_name="申请时间",auto_now_add=True)
    # 审核状态
    approval_status = models.CharField(max_length=20, verbose_name="审核状态", choices=ApprovalStatusEnum.choices,default=ApprovalStatusEnum.PENDING)
    # 审核意见
    approval_opinion = models.TextField(verbose_name="审核意见",blank=True,default='')
    # 审核时间
    approval_time = models.DateTimeField(verbose_name="审核时间",blank=True,null=True)
    # 审核人
    approval_by = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="审核人",blank=True,null=True)
    # 实际返回时间
    actual_return_time = models.DateTimeField(verbose_name="实际返回时间",blank=True,null=True)
    # 转诊返回出院小结（文件）
    referral_return_discharge = models.JSONField(verbose_name="转诊返回出院小结",blank=True,default=list)
    
    class Meta:
        verbose_name = "新生儿院内转诊申请单"
        verbose_name_plural = "新生儿院内转诊申请单"
        
    def __str__(self):
        return f"{self.baby.name}的院内转诊申请"
    
    
    # 查询新生儿是否有未处理的转诊单
    @classmethod
    def has_pending_referral(cls,baby):
        return cls.objects.filter(baby=baby, approval_status=ApprovalStatusEnum.PENDING).exists()
    
    # 审核通过转诊申请
    def approve_referral(self, staff,opinion):
        self.approval_status = ApprovalStatusEnum.APPROVED 
        self.referral_status = ReferralStatusEnum.PENDING_REFERRAL
        self.approval_opinion = opinion
        self.approval_time = timezone.now()
        self.approval_by = staff
        self.save()
        return self
    
    # 拒绝转诊申请
    def reject_referral(self, staff, opinion):
        self.approval_status = ApprovalStatusEnum.REJECTED
        self.referral_status = ReferralStatusEnum.REJECTED
        self.approval_opinion = opinion
        self.approval_time = timezone.now()
        self.approval_by = staff
        self.save()
        return self