from django.db import transaction
from django.db.models import Count, Q
from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.parse_time import parse_datetime_string
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from message.utils import send_wechat_app_visit_audit_message
from permissions.enum import PermissionEnum
from wx_mom.enum import MessageTypeEnum
from wx_mom.models import WxUnpaidMaternityMessage
from wx_mom.serializers import WechatAppVisitListSerializer
from .enum import WechatAppointmentStatusEnum, VisitorRecordStatusEnum
from .models import VisitorRecord, WechatAppVisitAppointment
from .serializers import VisitorRecordCreateSerializer, VisitorRecordDetailSerializer, VisitorRecordSerializer, \
    VisitorRecordUpdateSerializer,WechatAppVisitListSerializer


def get_visitor_record_str(visitor):
    return f"[{visitor.visitor_name}({visitor.visit_time.strftime('%Y-%m-%d %H:%M')})] - {visitor.vid}"

def get_wechat_app_visit_str(visit):
    return f"[{visit.visitor_name}({visit.visit_time.strftime('%Y-%m-%d %H:%M')})] - {visit.vid}"


class VisitorRecordBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]



# 访客登记列表
class VisitorRecordListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.VISITOR_VIEW
    serializer_class = VisitorRecordSerializer
    response_msg = "获取访客登记列表成功"
    error_response_msg = "获取访客登记列表失败"
    search_fields = ['visitor_name', 'visitor_phone', 'visit_time']
    audit_log_message = "访客登记"


    def get_queryset(self):
        
        base_queryset = VisitorRecord.objects.filter(maternity_center=self.request.user.maternity_center).order_by("-visit_time")
        
        # 状态
        aps = self.request.query_params.get('aps', None)
        # 来访时间
        apt = self.request.query_params.get('apt', None)
        
        if aps:
            if aps in VisitorRecordStatusEnum.values:
                base_queryset = base_queryset.filter(visit_status=aps)
            else:
                self.error_response_msg = "错误的状态参数"
                return None
        
        if apt:
            base_queryset = base_queryset.filter(visit_time__date=apt)

        return base_queryset

    def get_additional_data(self):
        today = timezone.now().date()
        
        month_start = today.replace(day=1)
        
        # 计算下个月第一天
        if today.month == 12:
            next_month_start = today.replace(year=today.year + 1, month=1, day=1)
        else:
            next_month_start = today.replace(month=today.month + 1, day=1)
        
        stats = VisitorRecord.objects.filter(
            maternity_center=self.request.user.maternity_center
        ).aggregate(
            visitor_count_today=Count('id', filter=Q(visit_time__date=today)),
            
            # 在访人数（今天来访但还没离开的，即leave_time为空）
            visitor_count_current=Count('id', filter=Q(
                visit_time__date=today,
                leave_flag=False,
                visit_status=VisitorRecordStatusEnum.VISITING
            )),
            
            # 今日已离开（今天来访且今天已离开的）
            visitor_count_left_today=Count('id', filter=Q(
                visit_time__date=today,
                leave_flag=True,
                actual_departure_time__date=today,
            )),
            
            # 本月总计来访
            visitor_count_this_month=Count('id', filter=Q(
            visit_time__date__gte=month_start,
            visit_time__date__lt=next_month_start            ))
        )
        
        return stats

# 访客详情
class VisitorRecordDetailView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_VIEW
    
    def get(self, request, vid):
        try:
            visitor_record = VisitorRecord.objects.get(vid=vid,maternity_center=request.user.maternity_center)
            serializer = VisitorRecordDetailSerializer(visitor_record)
            AuditLogCreator.create_query_audit_log(request, "访客登记", f"查看了{get_visitor_record_str(visitor_record)}<访客登记>详情")
            return make_response(code=0, msg='获取访客详情成功', data=serializer.data)
        except VisitorRecord.DoesNotExist:
            return make_response(code=-1, msg='访客记录不存在')
        except Exception as e:
            print(e)
            return make_response(code=-1, msg='获取访客详情失败，请稍后再试！')


# 创建访客登记
class VisitorRecordCreateView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def post(self, request):
        
        data = request.data.copy()
        
        visitor_count = data.get('visitor_count')
        temperature = data.get('temperature')
        aid = data.get('visited_aid')
        visit_status = data.get('visit_status')

        if visitor_count <= 0:
            return make_response(code=-1, msg="来访人数必须大于0")
        if temperature < 35.0 or temperature > 42.0:
            return make_response(code=-1, msg="体温数值异常，请重新测量")
        
        if visit_status not in VisitorRecordStatusEnum.values:
            return make_response(code=-1, msg="访问状态不正确")
        
        if aid:
            try:
                maternity_admission = MaternityAdmission.objects.prefetch_related('visitor_records').get(aid=aid, maternity_center=request.user.maternity_center)
                            
                if maternity_admission.visitor_records.filter(visit_status__in=[VisitorRecordStatusEnum.PENDING, VisitorRecordStatusEnum.VISITING]).exists():
                    return make_response(code=-1, msg="该产妇已有预约或正在访问中")
                
                data.pop('visited_aid')
                data['visited_admission'] = maternity_admission.id
                
            except MaternityAdmission.DoesNotExist:
                return make_response(code=-1, msg="产妇入住记录不存在")
        
        data['maternity_center'] = request.user.maternity_center.id

        
        serializer = VisitorRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            AuditLogCreator.create_create_audit_log(request, "访客登记", f"创建了{get_visitor_record_str(serializer.instance)}<访客登记>")
            return make_response(code=0, msg='创建访客登记成功', data=VisitorRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg=serializer.errors)
        
        
# 更新访客登记
class VisitorRecordUpdateView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def put(self, request, vid):
        
        try:
            visitor_record = VisitorRecord.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except VisitorRecord.DoesNotExist:
            return make_response(code=-1, msg="访客记录不存在")
        
        if visitor_record.leave_flag:
            return make_response(code=-1, msg="该访客已离开，无法更新记录。")
        
        data = request.data.copy()
        
        visitor_count = data.get('visitor_count')
        temperature = data.get('temperature')
        aid = data.get('visited_aid')
        visit_status = data.get('visit_status')

        if visitor_count <= 0:
            return make_response(code=-1, msg="来访人数必须大于0")
        if temperature < 35.0 or temperature > 42.0:
            return make_response(code=-1, msg="体温数值异常，请重新测量")
        
        if visit_status not in VisitorRecordStatusEnum.values:
            return make_response(code=-1, msg="访问状态不正确")
        
        if aid:
            try:
                maternity_admission = MaternityAdmission.objects.prefetch_related('visitor_records').get(aid=aid, maternity_center=request.user.maternity_center)            
                if maternity_admission.visitor_records.filter(visit_status__in=[VisitorRecordStatusEnum.PENDING, VisitorRecordStatusEnum.VISITING]).exclude(id=visitor_record.id).exists():
                    return make_response(code=-1, msg="该产妇已有预约或正在访问中")
                
                data.pop('visited_aid')
                data['visited_admission'] = maternity_admission.id
                
            except MaternityAdmission.DoesNotExist:
                return make_response(code=-1, msg="产妇入住记录不存在")

        serializer = VisitorRecordUpdateSerializer(visitor_record,data=data,partial=True)
        if serializer.is_valid():
            cfs = get_field_changes(visitor_record, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "访客登记", f"更新了{get_visitor_record_str(visitor_record)}<访客登记>", cfs)
            return make_response(code=0, msg='更新访客登记成功', data=VisitorRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg=serializer.errors)
        

# 标记访客访问中
class VisitorRecordVisitingView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def put(self, request, vid):
        try:
            visitor_record = VisitorRecord.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except VisitorRecord.DoesNotExist:
            return make_response(code=-1, msg="访客记录不存在")
        
        if visitor_record.visit_status != VisitorRecordStatusEnum.PENDING:
            return make_response(code=-1, msg=f"访客记录当前状态无法标记访问中，当前状态为：{visitor_record.get_visit_status_display()}")
        
        visitor_record.visit_status = VisitorRecordStatusEnum.VISITING
        visitor_record.save()
        AuditLogCreator.create_status_change_audit_log(request, "访客登记", f"标记{get_visitor_record_str(visitor_record)}<访客登记>为访问中")
        return make_response(code=0, msg='标记访客访问中成功')

# 标记访客离开
class VisitorRecordLeaveView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def put(self, request, vid):
        
        # 访问结果备注
        leave_remark = request.data.get('leave_remark')
        # 离开时间
        leave_time = request.data.get('leave_time')
        
        if not leave_time or not parse_datetime_string(leave_time,"%Y-%m-%d %H:%M:%S"):
            return make_response(code=-1, msg="未提供离开时间或离开时间格式不正确")        
        
        try:
            visitor_record = VisitorRecord.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except VisitorRecord.DoesNotExist:
            return make_response(code=-1, msg="访客记录不存在")
        
        if visitor_record.leave_flag:
            return make_response(code=-1, msg="该访客已离开，无法再次标记离开")
        
        visitor_record.leave_flag = True
        visitor_record.visit_result_remark = leave_remark
        visitor_record.actual_departure_time = leave_time
        visitor_record.visit_status = VisitorRecordStatusEnum.LEAVED
        visitor_record.save()
        
        AuditLogCreator.create_status_change_audit_log(request, "访客登记", f"标记{get_visitor_record_str(visitor_record)}<访客登记>为已离开")
        return make_response(code=0, msg='标记访客离开成功')



# 删除访客登记
class VisitorRecordDeleteView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def delete(self, request, vid):
        
        try:
            visitor_record = VisitorRecord.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except VisitorRecord.DoesNotExist:
            return make_response(code=-1, msg="访客记录不存在")
        
        if visitor_record.leave_flag:
            return make_response(code=-1, msg="该访客已离开，无法删除")
        
        if visitor_record.visit_status  ==  VisitorRecordStatusEnum.VISITING:
            return make_response(code=-1, msg="该访客正在访问中，无法删除")
        
        AuditLogCreator.create_delete_audit_log(request, "访客登记", f"删除了{get_visitor_record_str(visitor_record)}<访客登记>")
        visitor_record.delete()
        return make_response(code=0, msg='删除访客登记成功')




# 小程序预约参观列表
class WechatAppVisitListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.VISITOR_VIEW
    serializer_class = WechatAppVisitListSerializer
    
    response_msg = "获取小程序预约参观列表成功"
    error_response_msg = "获取小程序预约参观列表失败"
    search_fields = ['visitor_name', 'visitor_phone']
    audit_log_message = "小程序预约参观"


    def get_queryset(self):
        
        base_queryset = WechatAppVisitAppointment.objects.filter(maternity_center=self.request.user.maternity_center).order_by("-visit_time")
        
        # 状态
        aps = self.request.query_params.get('aps', None)
        # 来访时间
        apt = self.request.query_params.get('apt', None)
        
        if aps:
            if aps in WechatAppointmentStatusEnum.values:
                base_queryset = base_queryset.filter(appointment_status=aps)
            else:
                self.error_response_msg = "错误的状态参数"
                return None
        
        if apt:
            base_queryset = base_queryset.filter(visit_time__date=apt)

        return base_queryset


# 小程序预约参观审核
class WechatAppVisitAuditView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_AUDIT
    
    def put(self, request, vid):
        
        try:
            wechat_visit = WechatAppVisitAppointment.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except WechatAppVisitAppointment.DoesNotExist:
            return make_response(code=-1, msg="小程序预约参观不存在")
        
        if wechat_visit.appointment_status != WechatAppointmentStatusEnum.PENDING:
            return make_response(code=-1, msg=f"状态不正确，当前状态为：{wechat_visit.get_appointment_status_display()}")
        
        # 审核状态
        result = request.data.get('result')
        
        # 事务
        with transaction.atomic():
        
            if result == True:
                wechat_visit.mark_as_confirmed()
            else:
                wechat_visit.mark_as_rejected()
                
            WxUnpaidMaternityMessage.send_message(wechat_visit.maternity, wechat_visit.maternity_center, MessageTypeEnum.APPOINTMENT_CONFIRMATION, f'您的 {wechat_visit.visit_time.strftime("%Y-%m-%d %H:%M")} 预约参观申请已{"通过" if result else "被拒绝"}，您可登录小程序查看详情或电话联系客服！')
            
            if wechat_visit.maternity and wechat_visit.maternity.phone:
                send_wechat_app_visit_audit_message(
                    wechat_visit.maternity.phone,
                    {
                    'brand_name':wechat_visit.maternity_center.name,
                    'request_time':wechat_visit.visit_time.strftime("%Y-%m-%d %H:%M"),
                    'application_name':" 预约参观申请",
                    'audit_result':'确认' if result else "被拒绝",
                    # 'phone_number':wechat_visit.maternity_center.contact_us.first().contact_phone,
                })
            
        audit_result_text = "确认" if result else "拒绝"
        AuditLogCreator.create_request_audit_log(request, "小程序预约参观", f"审核了{get_wechat_app_visit_str(wechat_visit)}<小程序预约参观>，审核结果：{audit_result_text}")
        return make_response(code=0, msg=f'审核成功,当前状态为：{wechat_visit.get_appointment_status_display()}')

# 小程序预约参观标记完成
class WechatAppVisitCompleteView(VisitorRecordBaseView):
    
    staff_required_permission = PermissionEnum.VISITOR_EDIT
    
    def put(self, request, vid):
        
        try:
            wechat_visit = WechatAppVisitAppointment.objects.get(vid=vid,maternity_center=request.user.maternity_center)
        except WechatAppVisitAppointment.DoesNotExist:
            return make_response(code=-1, msg="小程序预约参观不存在")
        
        if wechat_visit.appointment_status != WechatAppointmentStatusEnum.CONFIRMED:
            return make_response(code=-1, msg=f"小程序预约参观状态不正确，当前状态为：{wechat_visit.get_appointment_status_display()}")
        
        wechat_visit.mark_as_completed()
        
        AuditLogCreator.create_status_change_audit_log(request, "小程序预约参观", f"标记{get_wechat_app_visit_str(wechat_visit)}<小程序预约参观>为完成")
        return make_response(code=0, msg='小程序预约参观标记完成成功')


    
