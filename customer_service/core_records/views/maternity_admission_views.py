from datetime import timedelta, datetime
from decimal import Decimal

from django.db import transaction
from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, MaternityOrStaffWithPermission, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum, DeliveryMethodEnum
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission, MaternityAdmissionRenew
from customer_service.core_records.serializers.baby import NewbornCreateSerializer
from customer_service.core_records.serializers.maternity_admission import DetailedMaternityAdmissionSerializer, \
    MaternityAdmissionCreateSerializer, MaternityAdmissionCustomerDetailSerializer, \
    MaternityAdmissionInHouseSelectListSerializer, MaternityAdmissionRenewSerializer, MaternityAdmissionUpdateSerializer
from customer_service.room.enum import RoomStatusEnum
from customer_service.room.models import Room
from organizational_management.charge.models import MaternityRenewCostInfo, Package
from organizational_management.charge.utils import create_maternity_cost_info
from organizational_management.charge.views import get_renew_cost_info_str
from permissions.enum import PermissionEnum
from user.models import Maternity, Staff
from user.serializers import MaternityDesensitizedSerializer, StaffSelectListSerializer


def get_maternity_admission_str(maternity_admission):
    if maternity_admission.room:
        room_number = maternity_admission.room.room_number
    else:
        room_number = '未分配'
    return f"[{maternity_admission.maternity.name}(房间{room_number})] - {maternity_admission.aid}"

def get_maternity_admission_renew_str(maternity_admission_renew):
    if maternity_admission_renew.maternity_admission.room:
        room_number = maternity_admission_renew.maternity_admission.room.room_number
    else:
        room_number = '未分配'
    return f"[{maternity_admission_renew.maternity_admission.maternity.name}(房间{room_number})] - {maternity_admission_renew.rid}"


# 入院记录列表
class MaternityAdmissionListView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_VIEW
    
    def get(self, request):
        status , resData = MaternityAdmission.get_maternity_admission_list(request)
        if not status:
            return make_response(code=-1, msg=resData)
        AuditLogCreator.create_query_audit_log(request, "入院记录", f"查看了<入院记录>列表")
        return make_response(code=0, msg="获取成功", data=resData)

# 入院记录基础视图类，包含共享的日期处理方法
class MaternityAdmissionBaseView(APIView):

    def _parse_date_string(self, date_str):
        # 将字符串日期转换为date对象
        if not date_str:
            return None
        if isinstance(date_str, str):
            try:
                return datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return None
        return date_str

    def _calculate_expected_checkout_date(self, data):
        residence_days = data.get('residence_days')
        check_in_status = data.get('check_in_status')
        expected_check_in_date = data.get('expected_check_in_date')
        actual_check_in_date = data.get('actual_check_in_date')

        if not residence_days:
            return None

        if check_in_status == 'CHECKED_IN' and actual_check_in_date:
            base_date = self._parse_date_string(actual_check_in_date)
        elif expected_check_in_date:
            base_date = self._parse_date_string(expected_check_in_date)
        else:
            return None

        if not base_date:
            return None

        return base_date + timedelta(days=residence_days)

    def _check_date_overlap(self, request, room, new_start_date, new_end_date, exclude_admission_id=None):

        # 检查日期范围是否与现有记录重叠
        existing_admissions = MaternityAdmission.objects.filter(
            room=room,
            maternity_center=request.user.maternity_center,
            check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN]
        )

        # 如果是更新操作，排除当前记录
        if exclude_admission_id:
            existing_admissions = existing_admissions.exclude(id=exclude_admission_id)

        # 检查每个现有记录是否与新记录有时间冲突
        for existing in existing_admissions:
            # 确定现有记录的有效日期范围
            if existing.check_in_status == CheckInStatusEnum.CHECKED_IN:
                # 现有记录是已入住：使用实际入住日期和预计退房日期
                if existing.actual_check_in_date and existing.expected_check_out_date:
                    existing_start_date = existing.actual_check_in_date
                    existing_end_date = existing.expected_check_out_date
                else:
                    continue  # 如果数据不完整，跳过这条记录
            else:
                # 现有记录是预约：使用预计入住日期和预计退房日期
                if existing.expected_check_in_date and existing.expected_check_out_date:
                    existing_start_date = existing.expected_check_in_date
                    existing_end_date = existing.expected_check_out_date
                else:
                    continue  # 如果数据不完整，跳过这条记录

            # 两个日期范围重叠的条件：新开始日期 < 现有结束日期 AND 现有开始日期 < 新结束日期
            if new_start_date < existing_end_date and existing_start_date < new_end_date:
                # 构建详细的错误信息
                existing_status_label = CheckInStatusEnum(existing.check_in_status).label
                existing_customer = existing.maternity.name if existing.maternity else "未知客户"

                error_msg = f"当前{room.room_number}房间在{new_start_date}到{new_end_date}期间与{existing_customer}的{existing_status_label}记录({existing_start_date}到{existing_end_date})存在时间冲突，无法办理入住！"
                return True, error_msg

        return False, None

    def _pre_save_check(self, request, data):
        
        maternity_id = data.get('maternity')
        
        if maternity_id:
            maternity = MaternityAdmission.objects.filter(maternity_id=maternity_id, maternity_center=request.user.maternity_center,check_in_status__in=[CheckInStatusEnum.CHECKED_IN,CheckInStatusEnum.RESERVED])
            if maternity:
                return make_response(code=-1, msg=f"当前产妇身份证号已存在入院记录，无法办理入住")
        
        room = data.get('room',None)
        if room:        
            
            try:
                room = Room.objects.get(rid=room, maternity_center=request.user.maternity_center)
            except Room.DoesNotExist:
                return make_response(code=-1, msg="房间不存在")
            
            if room.room_status != RoomStatusEnum.AVAILABLE:
                return make_response(code=-1, msg=f"当前{room.room_number}房间状态为{RoomStatusEnum(room.room_status).label}，无法办理入住！")

            # 获取基本信息
            _check_in_date = request.data.get('expected_check_in_date')
            _actual_check_in_date = request.data.get('actual_check_in_date')
            _check_in_status = request.data.get('check_in_status')

            if not _check_in_date:
                return make_response(code=-1, msg="办理入住时必须提供预计入住日期")

            _check_out_date = self._calculate_expected_checkout_date(request.data)
            if not _check_out_date:
                return make_response(code=-1, msg="办理入住时必须提供入住天数")

            # 根据新用户的状态确定检查的日期范围
            if _check_in_status == CheckInStatusEnum.CHECKED_IN:
                # 新用户是已入住状态：使用实际入住日期和预计退房日期
                if not _actual_check_in_date:
                    return make_response(code=-1, msg="直接办理入住状态必须提供实际入住日期")
                new_start_date = self._parse_date_string(_actual_check_in_date)
                new_end_date = _check_out_date
            else:
                # 新用户是预约状态：使用预计入住日期和预计退房日期
                new_start_date = self._parse_date_string(_check_in_date)
                new_end_date = _check_out_date

            if not new_start_date or not new_end_date:
                return make_response(code=-1, msg="日期格式错误")

            # 检查日期重叠
            is_overlap, error_msg = self._check_date_overlap(request, room, new_start_date, new_end_date)
            if is_overlap:
                return make_response(code=-1, msg=error_msg)
        
        return None

# 入院记录创建
class MaternityAdmissionCreateView(MaternityAdmissionBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT
    
    # 创建入院记录
    def post(self, request,uid):
        
        maternity = Maternity.get_maternity_by_uid(uid=uid,maternity_center=request.user.maternity_center)
        
        if not maternity:
            return make_response(code=-1, msg="产妇不存在")
        
        data = request.data.copy()
    
        result = self._pre_save_check(request, data)
        if result:
            return result
        
        cost_info = data.pop('cost_info')
        
        package = Package.get_package_by_rid(cost_info.get('package'),request.user.maternity_center)
        if not package:
            return make_response(code=-1, msg="套餐不存在")
        
        cost_info['package'] = package.id
        
        baby_list = data.pop('baby_list',None)
        
        with transaction.atomic():
            
            data['creator'] = request.user.id
            data['maternity'] = maternity.id
            data['maternity_center'] = request.user.maternity_center.id
            
            serializer = MaternityAdmissionCreateSerializer(data=data, context={'request': request})
            if not serializer.is_valid():
                return make_response(code=-1, msg='数据校验失败', data=serializer.errors)
            
            baby_serializers = []
            for baby in baby_list:
                ns = NewbornCreateSerializer(data=baby)
                if not ns.is_valid():
                    return make_response(code=-1, msg='数据校验失败', data=ns.errors)
                baby_serializers.append(ns)
            
            serializer.save()
            
            serializer.instance.update_expected_check_out_date()
            
            for ns in baby_serializers:
                ns.save(maternity_admission=serializer.instance)
                
            cres = create_maternity_cost_info(serializer.instance, package, cost_info,request.user.maternity_center)
            if cres:
                return make_response(code=-1, msg='数据校验失败', data=cres)
            
            AuditLogCreator.create_create_audit_log(request, "入院记录", f"创建了{get_maternity_admission_str(serializer.instance)}<入院记录>")
            return make_response(code=0, msg="入院记录创建成功", data=DetailedMaternityAdmissionSerializer(serializer.instance,context={'request': request}).data)
        
        
# 入院记录详情
class MaternityAdmissionDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.CHECK_IN_VIEW
    
    def get(self, request, aid):   
        try:
            admission = MaternityAdmission.objects.select_related('maternity', 'room').get(aid=aid, maternity_center=request.user.maternity_center)
            serializer = DetailedMaternityAdmissionSerializer(admission,context={'request': request})
            AuditLogCreator.create_query_audit_log(request, "入院记录", f"查看了{get_maternity_admission_str(admission)}<入院记录>详情")
            return make_response(code=0, msg="获取成功", data=serializer.data)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")
        

# 入院记录更新
class MaternityAdmissionUpdateView(MaternityAdmissionBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT

    def put(self, request, aid):
        try:
            admission = MaternityAdmission.objects.select_related('maternity', 'room').get(aid=aid, maternity_center=request.user.maternity_center)
            
            if admission.check_in_status == CheckInStatusEnum.CHECKED_IN:
                return make_response(code=-1, msg="当前状态为已入住状态，无法更新")

            # 如果更新涉及房间或日期信息，需要检查日期重叠
            room_rid = request.data.get('room')
            check_in_date = request.data.get('expected_check_in_date')
            actual_check_in_date = request.data.get('actual_check_in_date')
            check_in_status = request.data.get('check_in_status')
            residence_days = request.data.get('residence_days')

            # 如果涉及关键日期或房间信息的更新，进行重叠检查
            if any([room_rid, check_in_date, actual_check_in_date, check_in_status, residence_days]):
                # 确定要检查的房间（新房间或原房间）
                if room_rid:
                    try:
                        room = Room.objects.get(rid=room_rid, maternity_center=request.user.maternity_center)
                    except Room.DoesNotExist:
                        return make_response(code=-1, msg="房间不存在")
                else:
                    room = admission.room

                if room: 

                    merged_data = {
                        'expected_check_in_date': check_in_date or admission.expected_check_in_date,
                        'actual_check_in_date': actual_check_in_date or admission.actual_check_in_date,
                        'check_in_status': check_in_status or admission.check_in_status,
                        'residence_days': residence_days or admission.residence_days,
                    }

                    # 计算预计退房日期
                    check_out_date = self._calculate_expected_checkout_date(merged_data)
                    if not check_out_date:
                        return make_response(code=-1, msg="无法计算预计退房日期，请检查入住天数")

                    # 根据状态确定检查的日期范围
                    if merged_data['check_in_status'] == CheckInStatusEnum.CHECKED_IN:
                        if not merged_data['actual_check_in_date']:
                            return make_response(code=-1, msg="已入住状态必须提供实际入住日期")
                        new_start_date = self._parse_date_string(merged_data['actual_check_in_date']) 
                        new_end_date = check_out_date
                    else:
                        if not merged_data['expected_check_in_date']:
                            return make_response(code=-1, msg="预约状态必须提供预计入住日期")
                        new_start_date = self._parse_date_string(merged_data['expected_check_in_date'])
                        new_end_date = check_out_date

                    # 验证日期解析是否成功
                    if not new_start_date or not new_end_date:
                        return make_response(code=-1, msg="日期格式错误，请使用YYYY-MM-DD格式")

                    # 检查日期重叠（排除当前记录）
                    is_overlap, error_msg = self._check_date_overlap(request, room, new_start_date, new_end_date, exclude_admission_id=admission.id)
                    if is_overlap:
                        return make_response(code=-1, msg=error_msg)

            # 如果没有冲突，继续更新记录
            serializer = MaternityAdmissionUpdateSerializer(admission, data=request.data, partial=True, context={'request': request})
            if serializer.is_valid():
                cfs = get_field_changes(admission,serializer.validated_data)
                serializer.save()                
                AuditLogCreator.create_update_audit_log(request, "入院记录", f"更新了{get_maternity_admission_str(admission)}<入院记录>",cfs)
                return make_response(code=0, msg="更新成功", data=serializer.data)
            return make_response(code=-1, msg="数据校验失败", data=serializer.errors)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")
        
# 入院记录删除
class MaternityAdmissionDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT
    
    def delete(self, request, aid):
        try:
            admission = MaternityAdmission.objects.select_related('maternity', 'room').get(aid=aid, maternity_center=request.user.maternity_center)
            admission.delete()
            AuditLogCreator.create_delete_audit_log(request, "入院记录", f"删除了{get_maternity_admission_str(admission)}<入院记录>")
            return make_response(code=0, msg="删除成功")
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")


# 客户详情
class MaternityAdmissionCustomerDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_VIEW
    
    def get(self, request, aid):
        try:
            admission = MaternityAdmission.objects.select_related(
                'maternity', 'room', 'chief_nurse'
            ).prefetch_related(
                'newborns_by_admission',
                'maternity_recent_activity'
            ).get(aid=aid, maternity_center=request.user.maternity_center)        
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")
        
        serializer = MaternityAdmissionCustomerDetailSerializer(admission)
        AuditLogCreator.create_query_audit_log(request, "入院记录", f"查看了{get_maternity_admission_str(admission)}<客户详情>")
        return make_response(code=0, msg="获取客户详情成功", data=serializer.data)

# 续住入院记录
class MaternityAdmissionRenewView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT

    def put(self, request, aid):
        try:
            # 预加载相关数据
            admission = MaternityAdmission.objects.select_related('maternity', 'room').prefetch_related('maternity_cost_info').get(
                aid=aid, maternity_center=request.user.maternity_center
            )

            # 验证状态和参数
            if admission.check_in_status != CheckInStatusEnum.CHECKED_IN:
                return make_response(code=-1, msg="当前状态非已入住状态，无法续住")

            renew_days = request.data.get('renew_days', 0)
            if renew_days <= 0:
                return make_response(code=-1, msg="续住天数必须大于 0")

            cost_info = admission.maternity_cost_info.first()
            if not cost_info:
                return make_response(code=-1, msg=f"{admission.maternity.name} 未找到对应的费用信息，无法创建续住记录")

            # 计算新的退房日期
            new_expected_check_out_date = admission.expected_check_out_date + timedelta(days=renew_days)
            # 执行续住操作
            with transaction.atomic():
                # 创建续住记录
                renew_admission = MaternityAdmissionRenew.objects.create(
                    maternity_center=request.user.maternity_center,
                    maternity_admission=admission,
                    renew_days=renew_days,
                    renew_reason=request.data.get('renew_reason', ''),
                    original_expected_check_out_date=admission.expected_check_out_date,
                    new_expected_check_out_date=new_expected_check_out_date
                )

                # 创建续住费用记录
                renew_cost_info = MaternityRenewCostInfo.objects.create(
                    maternity_admission_renew=renew_admission,
                    maternity_cost_info=cost_info,
                    renew_days=renew_days,
                    renew_cost=Decimal(str(request.data.get('renew_price', 0))),
                    renew_cost_remark=request.data.get('renew_reason', '')
                )

                # 更新入院记录
                changed_fields = []
                changed_fields.append({
                    'field_name': 'expected_check_out_date',
                    'verbose_name': '预计退房日期',
                    'old_value': admission.expected_check_out_date,
                    'new_value': new_expected_check_out_date
                })
                admission.expected_check_out_date = new_expected_check_out_date
                changed_fields.append({
                    'field_name': 'residence_days',
                    'verbose_name': '入住天数',
                    'old_value': admission.residence_days,
                    'new_value': admission.residence_days + renew_days
                })
                admission.residence_days += renew_days
                admission.save()
                AuditLogCreator.create_update_audit_log(request, "入院记录", f"续住了{get_maternity_admission_str(admission)}<入院记录>",changed_fields)
                AuditLogCreator.create_create_audit_log(request, "入院记录", f"创建了{get_maternity_admission_renew_str(renew_admission)}<续住记录>")
                AuditLogCreator.create_create_audit_log(request, "续住费用信息", f"创建了{get_renew_cost_info_str(renew_cost_info)}<续住费用信息>")
                return make_response(code=0, msg=f"{admission.maternity.name} 续住成功，续住天数为：{renew_days}天", data=MaternityAdmissionRenewSerializer(renew_admission).data)

        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")
        except Exception as e:
            return make_response(code=-1, msg="数据校验失败",data=str(e))
            
# 入院记录办理入住
class MaternityAdmissionCheckInView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT
    
    def put(self, request, aid):
        
        # 校验房间和预计入住日期/实际入住日期/
        
        try:
            admission = MaternityAdmission.objects.select_related('maternity', 'room').get(aid=aid, maternity_center=request.user.maternity_center)
            
            if admission.check_in_status != CheckInStatusEnum.RESERVED:
                return make_response(code=-1, msg="当前状态非预约状态，无法办理入住")
            
            if not admission.actual_check_in_date :
                return make_response(code=-1, msg="实际入住日期不能为空,请先更新入院单再办理入住")
            
            if not admission.room:
                return make_response(code=-1, msg="房间不能为空,请先更新入院单再办理入住")
            
            # 校验分娩日期和分娩方式
            if not admission.actual_delivery_date:
                return make_response(code=-1, msg="实际分娩日期不能为空,请先更新入院单再办理入住")
            
            if not admission.delivery_hospital:
                return make_response(code=-1, msg="分娩医院不能为空,请先更新入院单再办理入住")
            
            if not admission.delivery_method or admission.delivery_method == DeliveryMethodEnum.UNBORN:
                return make_response(code=-1, msg="分娩方式不能为空,请先更新入院单再办理入住")
            
            if not admission.pregnancy_week:
                return make_response(code=-1, msg="孕周不能为空,请先更新入院单再办理入住")
            
            
            admission.check_in_status = CheckInStatusEnum.CHECKED_IN
            admission.actual_check_in_date = timezone.now().date()
            admission.save()
            
            status,resData = MaternityAdmission.get_maternity_admission_detail_by_id(request,admission.id)
            if not status:
                return make_response(code=-1, msg=resData)
            
            AuditLogCreator.create_status_change_audit_log(request, "入院记录", f"办理了{get_maternity_admission_str(admission)}<入院记录>的入住")
            return make_response(code=0, msg="办理入住成功",data=resData)
        
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="入院记录不存在")

# 员工列表视图
class StaffSelectListView(PaginationListBaseView):


    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.CHECK_IN_EDIT
    serializer_class = StaffSelectListSerializer
    response_msg = "获取员工列表成功"
    error_response_msg = "获取员工列表失败"
    search_fields = ['name']
    audit_log_message = "员工"
    
    def get_queryset(self):
        return Staff.objects.filter(maternity_center=self.request.user.maternity_center)
    
    
# 在住产妇选择列表
class MaternityAdmissionInHouseSelectListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_IN_EDIT,PermissionEnum.CHECK_IN_VIEW]
    serializer_class = MaternityAdmissionInHouseSelectListSerializer
    response_msg = "获取在住产妇列表成功"
    error_response_msg = "获取在住产妇列表失败"
    search_fields = ['maternity__name','maternity__phone','room__room_number']
    audit_log_message = "在住产妇"
    
    def get_queryset(self):
        return MaternityAdmission.objects.filter(maternity_center=self.request.user.maternity_center, check_in_status=CheckInStatusEnum.CHECKED_IN)
    
    

# 可办理入住产妇选择列表
class MaternitySelectListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.CHECK_IN_EDIT,PermissionEnum.CHECK_IN_VIEW]
    serializer_class = MaternityDesensitizedSerializer
    response_msg = "获取可办理入住产妇列表成功" 
    error_response_msg = "获取可办理入住产妇列表失败1"
    search_fields = ['name','phone','identity_number']
    audit_log_message = "可办理入住产妇"

    def get_queryset(self):
        
        base_query = Maternity.objects.filter(
            maternity_center=self.request.user.maternity_center       
        ).exclude(
            maternity_admission__check_in_status__in=[
                CheckInStatusEnum.CHECKED_IN,  
                CheckInStatusEnum.RESERVED,   
            ]
        ).order_by('-created_at')
        
        return base_query



