from dirtyfields import DirtyFieldsMixin
from django.db import models
from django.utils import timezone

from core.enum import BloodTypeEnum, GenderEnum
from core.generate_hashid import generate_newborn_care_one_record_code, generate_newborn_care_operation_record_code, \
    generate_newborn_care_two_record_code, generate_newborn_check_in_assessment_code, generate_newborn_code, \
    generate_newborn_daily_required_record_code, generate_newborn_feeding_record_code, \
    generate_newborn_month_assessment_code
from core.model import BaseModel, JSONListValidator, PositiveFloatField
from customer_service.core_records.enums.baby import (
    AnomalyTypeChoice, AnteriorFontanelleChoice, BirthInjuryTypeChoice, ButtocksAbnormalityChoice, ButtocksChoice,
    ExtremitiesChoice, ExtremityToneAndMovementChoice, LocationChoice, MonthCryChoice, MonthFeedingGuidanceChoice,
    MonthFeedingSituationChoice, MonthNutritionsDevelopmentChoice, MonthPreventColdChoice, MonthReactionChoice,
    MonthSkinStatusChoice, NutritionsDevelopmentChoice,
    OralMucosaAbnormalityC<PERSON><PERSON>, OralMucosaChoice, ReflexesChoice, SkinColorChoice, SuckingAbilitysChoice,
    FeedingMethodChoice,
    UmbilicalCordAbnormalityChoice, UmbilicalCordChoice, UrinationChoice, BowelMovementChoice,
    SkinStatusChoice, SkinAbnormalityChoice, ComplexionChoice, CryChoice, ReactionChoice, VaccineInjectionChoice
)
from user.models import Staff
from .maternity_admission import MaternityAdmission


# 婴儿核心记录单相关模型

# 婴儿基本信息记录
class Newborn(BaseModel, DirtyFieldsMixin):
    # 产妇入院信息
    maternity_admission = models.ForeignKey(MaternityAdmission, related_name="newborns_by_admission",on_delete=models.CASCADE, verbose_name="产妇入院单", blank=True, null=True)
    # 姓名
    name = models.CharField(max_length=50, verbose_name="姓名")
    # 性别
    gender = models.IntegerField(verbose_name="性别", choices=GenderEnum.choices, default=GenderEnum.UNKNOWN)
    # 出生时间
    birth_time = models.DateTimeField(verbose_name="出生时间",default=timezone.now)
    # 出生体重
    birth_weight = models.DecimalField(max_digits=6, decimal_places=2, verbose_name="出生体重(g)",default=0)
    # 出生身长
    birth_length = models.DecimalField(max_digits=6, decimal_places=2, verbose_name="出生身长(cm)",default=0)
    # 过敏史
    allergy_history = models.TextField(blank=True, verbose_name="过敏史")
    # 出生孕周
    birth_week = models.PositiveIntegerField(verbose_name="出生孕周",blank=True, null=True)
    # 新生儿编号
    nid = models.CharField(max_length=50, verbose_name="新生儿编号",blank=True, default=generate_newborn_code)
    # 手卡号
    hand_card_number = models.CharField(max_length=50, verbose_name="手卡号",blank=True, default='')
    
    def __str__(self):
        return f"{self.name} ({self.birth_time})"

    class Meta:
        verbose_name = "婴儿基本信息"
        verbose_name_plural = "婴儿基本信息"
        
    @classmethod
    def get_newborn_by_nid(cls,nid,maternity_center):
        try:
            return cls.objects.get(nid=nid,maternity_admission__maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    
    @classmethod
    def check_newborn_exists(cls,nid,maternity_center_id):
        return cls.objects.filter(nid=nid,maternity_admission__maternity_center=maternity_center_id).exists()
    
    @staticmethod
    def get_newborn_name_by_id(id):
        return Newborn.objects.get(id=id).name
        
# 婴儿每日必填
class NewbornDailyRequiredRecord(BaseModel, DirtyFieldsMixin):
    # 婴儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, related_name='newborn_daily_required')
    # 记录日期
    record_date = models.DateTimeField(verbose_name='记录日期')
    # 黄疸
    jaundice = PositiveFloatField(verbose_name='黄疸')
    # 体重
    weight = PositiveFloatField(verbose_name='体重')
    # 体温
    temperature = PositiveFloatField(verbose_name='体温')
    # 睡眠情况
    sleep_status = models.CharField(max_length=50,verbose_name='睡眠情况')
    # 排泄情况
    bowel_movement_status = models.CharField(max_length=50,verbose_name='排泄情况')
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_daily_required',verbose_name='创建人')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_newborn_daily_required_record_code)
    
    
    class Meta:
        verbose_name = "婴儿每日必填"
        verbose_name_plural = "婴儿每日必填"
        unique_together = [['newborn', 'record_date']]
        
    def __str__(self):
        return f"{self.newborn.name} ({self.record_date}) 的每日必填"
    
    # 检查是否存在每日必填记录
    @classmethod
    def check_record_date_exists(cls,nid,record_date,exclude_id=None):
        if exclude_id:
            return cls.objects.filter(newborn__nid=nid,record_date=record_date).exclude(id=exclude_id).exists()
        return cls.objects.filter(newborn__nid=nid,record_date=record_date).exists()

# 新生儿入住评估记录
class NewbornCheckInAssessment(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", related_name="newborn_check_in_assessments")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_assessments',verbose_name="创建人", )
    # 入所时间
    admission_time = models.DateTimeField(verbose_name="入所时间",default=timezone.now)
    # 血型
    blood_type = models.CharField(max_length=30, choices=BloodTypeEnum.choices, verbose_name="血型",blank=True,default=BloodTypeEnum.UNKNOWN)
    # Apgar评分
    apgar_score = models.PositiveIntegerField(verbose_name="Apgar评分",blank=True, null=True)
    # 出院小结
    discharge_summary = models.BooleanField(verbose_name="出院小结",default=False)
    # 住院治疗史
    hospitalization_history = models.BooleanField(verbose_name="住院治疗史",default=False)
    # 住院治疗史—疾病名称
    hospitalization_disease = models.CharField(max_length=200,  verbose_name="住院治疗史—疾病名称",blank=True,default='')
    # 现是否用药
    current_medication = models.BooleanField(verbose_name="现是否用药",default=False)
    # 现是否用药—有—药物名称
    medication_name = models.CharField(max_length=200, verbose_name="现是否用药—药物名称",blank=True,default='')
    # 过敏史
    allergy_history = models.TextField(verbose_name="过敏史",blank=True,default='')
    # 新生儿特殊情况
    special_conditions = models.TextField(verbose_name="新生儿特殊情况",blank=True,default='')
    # 出院黄疸指数
    discharge_jaundice = PositiveFloatField(verbose_name="出院黄疸指数(mg/dL)",blank=True, null=True)
    # 喂养情况
    feeding_type = models.CharField(max_length=30, choices=FeedingMethodChoice.choices, verbose_name="喂养情况",blank=True,default=FeedingMethodChoice.UNKNOWN)
    # 喂养情况—奶粉品牌
    formula_brand = models.TextField( verbose_name="喂养情况—奶粉品牌",blank=True,default='')
    # 喂养情况—配方奶与母乳量
    formula_breastmilk_ratio = models.TextField(verbose_name="喂养情况—配方奶与母乳量",blank=True,default='')
    # 排尿情况
    urination_status = models.CharField(max_length=30, choices=UrinationChoice.choices, verbose_name="排尿情况",blank=True,default=UrinationChoice.UNKNOWN)
    # 排尿次数
    normal_urination_times = models.IntegerField(blank=True, null=True, verbose_name="排尿—次数（正常与尿少共用）")
    # 惯用尿布品牌
    usual_diaper_brand = models.TextField(verbose_name="惯用尿布品牌",blank=True,default='')
    # 排便情况
    bowel_movement_status = models.CharField(max_length=30, choices=BowelMovementChoice.choices, verbose_name="排便情况",blank=True,default=BowelMovementChoice.NORMAL)
    # 排便次数
    bowel_times = models.PositiveIntegerField(verbose_name="排便次数",blank=True, null=True)
    # 体温
    temperature = PositiveFloatField(verbose_name="体温(℃)",blank=True, null=True)
    # 心率
    heart_rate = models.PositiveIntegerField(verbose_name="心率(次/分钟)",blank=True, null=True)
    # 呼吸
    respiration = models.PositiveIntegerField(verbose_name="呼吸(次/分钟)",blank=True, null=True)
    # 体重
    weight = PositiveFloatField(verbose_name="体重(g)",blank=True, null=True)
    # 血氧
    spo2 = PositiveFloatField(verbose_name="血氧(%)",blank=True, null=True)
    # 面色
    complexion = models.CharField(max_length=30, choices=ComplexionChoice.choices, verbose_name="面色",blank=True,default=ComplexionChoice.UNKNOWN)
    # 哭声
    cry = models.CharField(max_length=30, choices=CryChoice.choices, verbose_name="哭声",blank=True,default=CryChoice.UNKNOWN)
    # 反应
    reaction = models.CharField(max_length=30, choices=ReactionChoice.choices, verbose_name="反应",blank=True,default=ReactionChoice.UNKNOWN)
    # 四肢
    extremities = models.CharField(max_length=30, choices=ExtremitiesChoice.choices, verbose_name="四肢",blank=True,default=ExtremitiesChoice.UNKNOWN)
    # 四肢张力及活动
    extremity_tone = models.CharField(max_length=30, choices=ExtremityToneAndMovementChoice.choices, verbose_name="四肢张力及活动",blank=True,default=ExtremityToneAndMovementChoice.UNKNOWN)
    # 四肢张力及活动受限部位
    extremity_restricted_area = models.TextField(verbose_name="四肢张力及活动受限部位",blank=True,default='')
    # 产伤
    birth_injury = models.BooleanField(verbose_name="产伤",blank=True,default=False)
    # 产伤类型
    birth_injury_type = models.CharField(max_length=30, choices=BirthInjuryTypeChoice.choices, verbose_name="产伤类型",blank=True,default=BirthInjuryTypeChoice.UNKNOWN)
    # 产伤，皮损部位
    birth_injury_skin_lesion_location = models.TextField(verbose_name="产伤，皮损部位",blank=True,default='')
    # 心脏杂音
    heart_murmur = models.BooleanField(verbose_name="心脏杂音",blank=True,default=False)
    # 神经反射
    reflexes = models.CharField(max_length=30, choices=ReflexesChoice.choices, verbose_name="神经反射",blank=True,default=ReflexesChoice.UNKNOWN)
    # 营养发育
    nutrition_development = models.CharField(max_length=30, choices=NutritionsDevelopmentChoice.choices, verbose_name="营养发育",blank=True,default=NutritionsDevelopmentChoice.UNKNOWN)
    # 营养发育滞后位于多少百分位数
    nutrition_delayed_percentile = PositiveFloatField(verbose_name="营养发育滞后位于多少百分位数",blank=True, null=True)
    # 全身皮肤
    skin_status = models.CharField(max_length=30, choices=SkinStatusChoice.choices, verbose_name="全身皮肤",blank=True,default=SkinStatusChoice.UNKNOWN)
    # 全身皮肤—异常
    skin_abnormality = models.CharField(max_length=30, choices=SkinAbnormalityChoice.choices, verbose_name="全身皮肤—异常",blank=True,default=SkinAbnormalityChoice.UNKNOWN)
    # 全身皮肤—皮损部位
    skin_lesion_location = models.TextField(verbose_name="全身皮肤—皮损部位",blank=True,default='')
    # 全身皮肤—红斑部位
    skin_erythema_location = models.TextField(verbose_name="全身皮肤—红斑部位",blank=True,default='')
    # 全身皮肤—皮疹部位
    skin_rash_location = models.TextField(verbose_name="全身皮肤—皮疹部位",blank=True,default='')
    # 全身皮肤—水肿部位
    skin_edema_location = models.TextField(verbose_name="全身皮肤—水肿部位",blank=True,default='')
    # 全身皮肤—其他部位
    skin_other_location = models.TextField(verbose_name="全身皮肤—其他部位",blank=True,default='')
    # 头围
    head_circumference = PositiveFloatField(verbose_name="头围(cm)",blank=True, null=True)
    # 前囟
    anterior_fontanelle = models.CharField(max_length=30, choices=AnteriorFontanelleChoice.choices, verbose_name="前囟",blank=True,default=AnteriorFontanelleChoice.UNKNOWN)
    # 前囟大小
    fontanelle_size = models.TextField(verbose_name="前囟大小",blank=True,default='')
    # 口腔黏膜
    oral_mucosa = models.CharField(max_length=30, choices=OralMucosaChoice.choices, verbose_name="口腔黏膜",blank=True,default=OralMucosaChoice.UNKNOWN)
    # 口腔黏膜—异常
    oral_mucosa_abnormality = models.CharField(max_length=30, choices=OralMucosaAbnormalityChoice.choices, verbose_name="口腔黏膜—异常",blank=True,default=OralMucosaAbnormalityChoice.UNKNOWN)
    # 眼结膜充血和分泌物
    conjunctiva_status = models.BooleanField(verbose_name="眼结膜充血和分泌物", blank=True, default=False)
    # 巩膜黄染
    scleral_jaundice = models.BooleanField(verbose_name="巩膜黄染", blank=True, default=False)
    # 新生儿是否出现畸形
    congenital_anomaly = models.BooleanField(verbose_name="新生儿是否出现畸形", blank=True, default=False)
    # 畸形类型
    anomaly_type = models.CharField(max_length=30, choices=AnomalyTypeChoice.choices, verbose_name="畸形类型",blank=True,default=AnomalyTypeChoice.UNKNOWN)
    # 畸形类型—其他
    anomaly_type_other = models.TextField(verbose_name="畸形类型—其他",blank=True,default='')
    # 脐部
    umbilical_cord = models.CharField(max_length=30, choices=UmbilicalCordChoice.choices, verbose_name="脐部",blank=True,default=UmbilicalCordChoice.UNKNOWN)
    # 脐部异常
    umbilical_cord_abnormality = models.CharField(max_length=30, choices=UmbilicalCordAbnormalityChoice.choices, verbose_name="脐部异常",blank=True,default=UmbilicalCordAbnormalityChoice.UNKNOWN)
    # 轮脐红肿
    umbilical_cord_red_and_swollen = models.BooleanField(verbose_name="轮脐红肿", blank=True, default=False)
    # 脐部脱落
    umbilical_cord_fall_off = models.BooleanField(verbose_name="脐部脱落", blank=True, default=False)
    # 吸吮情况
    sucking_ability = models.CharField(max_length=30, choices=SuckingAbilitysChoice.choices, verbose_name="吸吮情况",blank=True,default=SuckingAbilitysChoice.UNKNOWN)
    # 臀部
    buttocks = models.CharField(max_length=30, choices=ButtocksChoice.choices, verbose_name="臀部",blank=True,default=ButtocksChoice.UNKNOWN)
    # 臀部异常
    buttocks_abnormality = models.CharField(max_length=30, choices=ButtocksAbnormalityChoice.choices, verbose_name="臀部异常",blank=True,default=ButtocksAbnormalityChoice.UNKNOWN)
    # 假性月经
    pseudomenstruation = models.BooleanField(verbose_name="假性月经", blank=True, default=False)
    # 预防接种
    vaccine_injection = models.CharField(max_length=30, choices=VaccineInjectionChoice.choices, verbose_name="预防接种",blank=True,default=VaccineInjectionChoice.UNKNOWN)
    # 预防接种—其他
    vaccine_injection_other = models.TextField(verbose_name="预防接种—其他",blank=True,default='')
    # 入所黄疸指数
    admission_jaundice = PositiveFloatField(verbose_name="入所黄疸指数(mg/dL)",blank=True, null=True)
    # 部位
    location = models.CharField(max_length=30, choices=LocationChoice.choices, verbose_name="部位",blank=True,default=LocationChoice.UNKNOWN)
    # 是否需要光疗
    need_phototherapy = models.BooleanField(verbose_name="是否需要光疗", blank=True, default=False)
    # 其他特殊情况
    other_special_conditions = models.TextField(verbose_name="其他特殊情况",blank=True,default='')
    # 婴儿护理要点
    baby_care_points = models.TextField(verbose_name="婴儿护理要点",blank=True,default='')
    # 签名
    signature = models.CharField(max_length=100, verbose_name="签名",blank=True,default='')
    # 编号
    assessment_id = models.CharField(max_length=50, verbose_name="评估编号",blank=True, default=generate_newborn_check_in_assessment_code)
    # 评估时间
    assessment_time = models.DateTimeField(verbose_name="评估时间",blank=True, null=True)
    

    def __str__(self):
        return f"{self.newborn.name} 的评估 ({self.assessment_time})"

    class Meta:
        verbose_name = "新生儿入住评估单"
        verbose_name_plural = "新生儿入住评估单"


# 新生儿护理记录单（1）
class NewbornCareOneRecord(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", related_name="newborn_care_one_records")
    # 日期
    record_date = models.DateField(verbose_name="日期")
    # 出生天数
    days_after_birth = models.PositiveIntegerField(verbose_name="出生天数")
    # 体重
    weight = PositiveFloatField(verbose_name="体重(g)",blank=True, null=True)
    # 身长
    length = PositiveFloatField(verbose_name="身长(cm)",blank=True, null=True)
    # 哭声
    cry = models.CharField(max_length=200, verbose_name="哭声",blank=True, default='')
    # 体温
    temperature = PositiveFloatField(verbose_name="体温(℃)",blank=True, null=True)
    # 小便
    urine = models.CharField(max_length=200, verbose_name="小便",blank=True, default='')
    # 大便次数
    bowel_movement_frequency = models.PositiveIntegerField(verbose_name="大便次数",blank=True, null=True)
    # 大便颜色
    bowel_movement_color = models.CharField(max_length=50, verbose_name="大便颜色",blank=True, default='')
    # 大便性质
    bowel_movement_consistency = models.CharField(max_length=50, verbose_name="大便性质",blank=True, default='')
    # 前囟
    anterior_fontanelle = models.CharField(max_length=50, verbose_name="前囟",blank=True, default='')
    # 指导意见
    guidance = models.TextField(verbose_name="指导意见",blank=True, default='')
    # 签名
    caregiver_signature = models.CharField(max_length=50, verbose_name="签名",blank=True, default='')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_newborn_care_one_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_care_one_records',
                                verbose_name="创建人", blank=True, null=True)
    
    
    class Meta:
        verbose_name = "新生儿护理记录单（1）"
        verbose_name_plural = "新生儿护理记录单（1）"
        unique_together = [['newborn', 'record_date']] 
        
    
    def __str__(self):
        return f"{self.newborn.name} 的护理记录单（1） ({self.record_date})"
    
    @classmethod
    def check_record_date_exists(cls, nid, record_date,maternity_center,exclude_id=None):
        return cls.objects.filter(newborn__nid=nid, record_date=record_date,newborn__maternity_admission__maternity_center=maternity_center).exclude(id=exclude_id).exists()


# 新生儿护理记录单（2）
class NewbornCareTwoRecord(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", related_name="newborn_care_two_records")
    # 日期
    record_date = models.DateField(verbose_name="日期")
    # 皮肤黄疸
    skin_jaundice = models.CharField(max_length=100, verbose_name="皮肤—黄疸",blank=True, default='')
    # 皮肤红润
    skin_ruddy = models.CharField(max_length=100, verbose_name="皮肤—红润",blank=True, default='')
    # 皮肤红斑
    skin_erythema = models.CharField(max_length=100, verbose_name="皮肤—红斑",blank=True, default='')
    # 皮肤糜烂
    skin_erosion = models.CharField(max_length=100, verbose_name="皮肤—糜烂",blank=True, default='')
    # 皮肤脓疱疹
    skin_pustule = models.CharField(max_length=100, verbose_name="皮肤—脓疱疹",blank=True, default='')
    # 皮肤湿疹
    skin_eczema = models.CharField(max_length=100, verbose_name="皮肤—湿疹",blank=True, default='')
    # 皮肤尿布疹
    skin_diaper_rash = models.CharField(max_length=100, verbose_name="皮肤—尿布疹",blank=True, default='')
    # 眼睛正常
    eyes_normal = models.CharField(max_length=100, verbose_name="眼——正常",blank=True, default='')
    # 眼睛分泌物
    eyes_discharge = models.CharField(max_length=100, verbose_name="眼——分泌物",blank=True, default='')
    # 口腔光滑
    oral_smooth = models.CharField(max_length=100, verbose_name="口腔——光滑",blank=True, default='')
    # 口腔破溃
    oral_ulcer = models.CharField(max_length=100, verbose_name="口腔——破溃",blank=True, default='')
    # 口腔鹅口疮
    oral_thrush = models.CharField(max_length=100, verbose_name="口腔——鹅口疮",blank=True, default='')
    # 脐带干燥
    umbilical_dry = models.CharField(max_length=100, verbose_name="脐带——干燥",blank=True, default='')
    # 脐带出血
    umbilical_bleeding = models.CharField(max_length=100, verbose_name="脐带——出血",blank=True, default='')
    # 脐带脱落
    umbilical_detached = models.CharField(max_length=100, verbose_name="脐带——脱落",blank=True, default='')
    # 呕吐
    vomiting = models.CharField(max_length=100, verbose_name="呕吐",blank=True, default='')
    # 四肢张力
    limb_tone = models.CharField(max_length=100, verbose_name="四肢张力",blank=True, default='')
    # 产伤
    birth_injury = models.CharField(max_length=100, verbose_name="产伤",blank=True, default='')
    # 黄疸值
    jaundice_value = PositiveFloatField(verbose_name="黄疸值(mg/dL)",blank=True, null=True)
    # 签名
    caregiver_signature = models.CharField(max_length=50, verbose_name="签名",blank=True, default='')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_newborn_care_two_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_care_two_records',
                                verbose_name="创建人", blank=True, null=True)
    
    
    class Meta:
        verbose_name = "新生儿护理记录单（2）"
        verbose_name_plural = "新生儿护理记录单（2）"
        unique_together = [['newborn', 'record_date']]  # 添加这行
        
    
    def __str__(self):
        return f"{self.newborn.name} 的护理记录单（2） ({self.record_date})"
    
    @classmethod
    def check_record_date_exists(cls, nid, record_date,maternity_center,exclude_id=None):
        return cls.objects.filter(newborn__nid=nid, record_date=record_date,newborn__maternity_admission__maternity_center=maternity_center).exclude(id=exclude_id).exists()
    


# 新生儿喂养记录
class NewbornFeedingRecord(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", db_index=True, related_name="newborn_feeding_records")
    # 日期
    record_time = models.DateTimeField(verbose_name="日期/时间")
    # 喂养方式
    feeding_method = models.CharField(max_length=30, choices=FeedingMethodChoice.choices, verbose_name="喂养方式",blank=True,default=FeedingMethodChoice.UNKNOWN)
    # 母乳喂养左侧时间
    breast_feeding_left_time = models.PositiveIntegerField(verbose_name="母乳喂养左侧时间",blank=True, null=True)
    # 母乳喂养右侧时间
    breast_feeding_right_time = models.PositiveIntegerField(verbose_name="母乳喂养右侧时间",blank=True, null=True)
    # 人工喂养指征
    artificial_feeding_indicators = models.CharField(max_length=100, verbose_name="人工喂养指征",blank=True, default='')
    # 人工喂养量
    artificial_feeding_amount = PositiveFloatField(verbose_name="人工喂养量",blank=True, null=True)
    # 混合喂养-自喂-时间
    mixed_feeding_self_feeding_time = models.PositiveIntegerField(verbose_name="混合喂养-自喂-时间",blank=True, null=True)
    # 混合喂养-人喂-毫升
    mixed_feeding_human_feeding_ml = PositiveFloatField(verbose_name="混合喂养-人喂-毫升",blank=True, null=True)
    # 其他
    other_feeding = models.CharField(max_length=100, verbose_name="其他",blank=True, default='')
    # 签名
    caregiver_signature = models.CharField(max_length=50, verbose_name="签名",blank=True, default='')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_newborn_feeding_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='newborn_feeding_records_created_by_staff',
                                verbose_name="创建人", blank=True, null=True)

    class Meta:
        verbose_name = "新生儿喂养记录"
        verbose_name_plural = "新生儿喂养记录"
        unique_together = [['newborn', 'record_time']]



    def __str__(self):
        return f"{self.newborn.name} 的喂养记录 ({self.record_time})"
    
    @classmethod
    def check_record_time_exists(cls, nid, record_time,maternity_center,exclude_id=None):
        return cls.objects.filter(newborn__nid=nid, record_time=record_time,newborn__maternity_admission__maternity_center=maternity_center).exclude(id=exclude_id).exists()
    
    
# 新生儿护理操作
class NewbornCareOperationRecord(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", db_index=True, related_name="newborn_care_operation_records")
    # 日期
    record_date = models.DateField(verbose_name="日期")
    # 出生天数
    days_after_birth = models.PositiveIntegerField(verbose_name="出生天数")
    # 体温
    temperature = PositiveFloatField(verbose_name="体温(℃)",blank=True, null=True)
    # 体重
    weight = PositiveFloatField(verbose_name="体重(g)",blank=True, null=True)
    # 脐护
    umbilical_care = models.CharField(max_length=100, verbose_name="脐护",blank=True, default='')
    # 洗澡
    bath = models.CharField(max_length=100, verbose_name="洗澡",blank=True, default='')
    # 游泳
    swimming = models.CharField(max_length=100, verbose_name="游泳",blank=True, default='')
    # 抚触
    massage = models.CharField(max_length=100, verbose_name="抚触",blank=True, default='')
    # 推拿
    acupressure = models.CharField(max_length=100, verbose_name="推拿",blank=True, default='')
    # SPA
    spa = models.CharField(max_length=1000, verbose_name="SPA",blank=True, default='')
    # 其他情况
    other_situation = models.TextField(verbose_name="其他情况",blank=True, default='')
    # 操作者
    operator = models.CharField(max_length=50, verbose_name="操作者",blank=True, default='')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_newborn_care_operation_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_care_operation_records',
                                verbose_name="创建人", blank=True, null=True)
    
    class Meta:
        verbose_name = "新生儿护理操作记录"
        verbose_name_plural = "新生儿护理操作记录"
        unique_together = [['newborn', 'record_date']]  # 添加这行

    def __str__(self):
        return f"{self.newborn.name} 的护理操作记录 ({self.record_date})"
    
    @classmethod
    def check_record_date_exists(cls, nid, record_date,maternity_center,exclude_id=None):
        return cls.objects.filter(newborn__nid=nid, record_date=record_date,newborn__maternity_admission__maternity_center=maternity_center).exclude(id=exclude_id).exists()
    
    
    
# 新生儿满月评估
class NewbornMonthAssessment(BaseModel, DirtyFieldsMixin):
    # 新生儿
    newborn = models.ForeignKey(Newborn, on_delete=models.CASCADE, verbose_name="新生儿", db_index=True, related_name="newborn_month_assessments")
    # 评估时间
    assessment_date = models.DateTimeField(verbose_name="评估时间")
    # 体温
    temperature = PositiveFloatField(verbose_name="体温(℃)",blank=True, null=True)
    # 体重
    weight = PositiveFloatField(verbose_name="体重(g)",blank=True, null=True)
    # 营养发育
    nutrition_development = models.CharField(max_length=30, choices=MonthNutritionsDevelopmentChoice.choices, verbose_name="营养发育",blank=True,default=MonthNutritionsDevelopmentChoice.UNKNOWN)
    # 面色
    complexion = models.CharField(max_length=30, choices=SkinColorChoice.choices, verbose_name="面色",blank=True,default=SkinColorChoice.UNKNOWN)
    # 哭声
    cry = models.CharField(max_length=30, choices=MonthCryChoice.choices, verbose_name="哭声",blank=True,default=MonthCryChoice.UNKNOWN)
    # 反应
    reaction = models.CharField(max_length=30, choices=MonthReactionChoice.choices, verbose_name="反应",blank=True,default=MonthReactionChoice.UNKNOWN)
    # 皮肤
    skin = models.JSONField(verbose_name="皮肤",blank=True,validators=[JSONListValidator(MonthSkinStatusChoice.choices)],default=list)
    # 破损部位
    damaged_part = models.CharField(max_length=100, verbose_name="破损部位",blank=True,default='')
    # 皮疹部位
    rash_part = models.CharField(max_length=100, verbose_name="皮疹部位",blank=True,default='')
    # 喂养情况
    feeding_situation = models.CharField(max_length=70, choices=MonthFeedingSituationChoice.choices, verbose_name="喂养情况",blank=True,default=MonthFeedingSituationChoice.UNKNOWN)
    # 奶粉品牌
    formula_brand = models.CharField(max_length=100, verbose_name="奶粉品牌",blank=True,default='')
    # 排尿正常
    urine_normal = models.BooleanField(verbose_name="排尿正常",blank=True,default=False)
    # 排尿次数（times）
    urine_times = models.PositiveIntegerField(verbose_name="排尿次数",blank=True, null=True)
    # 排便正常
    bowel_movement_normal = models.BooleanField(verbose_name="大便正常",blank=True,default=False)
    # 排便习惯次数（times）
    bowel_movement_usual_times = models.PositiveIntegerField(verbose_name="排便习惯次数",blank=True, null=True)
    # 便秘次数(times)
    constipations_times = models.PositiveIntegerField(verbose_name="便秘次数",blank=True, null=True)
    # 腹泻次数
    diarrhea_times = models.PositiveIntegerField(verbose_name="腹泻次数",blank=True, null=True)
    # 肌张力及活动
    muscle_tone_and_activity = models.CharField(max_length=100, choices=ExtremityToneAndMovementChoice.choices, verbose_name="肌张力及活动",blank=True,default=ExtremityToneAndMovementChoice.UNKNOWN)
    # 肌张力及活动受限部位
    muscle_tone_and_activity_limited_part = models.CharField(max_length=100, verbose_name="肌张力及活动受限部位",blank=True,default='')
    # 现存护理问题
    current_nursing_problems = models.BooleanField(verbose_name="现存护理问题",blank=True,default=False)
    # 现存护理问题描述
    current_nursing_problems_description = models.TextField(verbose_name="现存护理问题描述",blank=True, default='')
    # 喂养指导
    feeding_guidance = models.CharField(max_length=70, choices=MonthFeedingGuidanceChoice.choices, verbose_name="喂养指导",blank=True,default=MonthFeedingGuidanceChoice.UNKNOWN)
    # 喂养指导-其他-描述
    feeding_guidance_other_description = models.TextField(verbose_name="喂养指导-其他-描述",blank=True, default='')
    # 预防感冒
    prevent_cold = models.CharField(max_length=70, choices=MonthPreventColdChoice.choices, verbose_name="预防感冒",blank=True,default=MonthPreventColdChoice.UNKNOWN)
    # 编号
    assessment_id = models.CharField(max_length=50, verbose_name="评估编号",blank=True, default=generate_newborn_month_assessment_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_newborn_month_assessments',
                                verbose_name="创建人", blank=True, null=True)
    
    class Meta:
        verbose_name = "新生儿满月评估"
        verbose_name_plural = "新生儿满月评估"
        unique_together = [['newborn']]
        
    def __str__(self):
        return f"{self.newborn.name} 的满月评估 ({self.assessment_date if self.assessment_date else '-'})"
    
    
    
    