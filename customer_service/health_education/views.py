from django.db.models import Q
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.parse_time import parse_datetime_to_shanghai_time
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.core_records.serializers.maternity_admission import MaternityAdmissionInHouseSelectListSerializer
from permissions.enum import PermissionEnum
from .enum import FormatEnum, HealthEducationWayEnum
from .models import HealthEducationContent, HealthEducationRecord
from .serializers import HealehEducationRecordDetailSerializer, HealthEducationContentListSerializer, HealthEducationContentCreateSerializer, \
    HealehEducationContentDetailSerializer, HealthEducationContentUpdateSerializer, HealthEducationRecordCreateSerializer, HealthEducationRecordListSerializer, HealthEducationRecordUpdateSerializer


def get_health_education_content_str(content):
    return f"[{content.title}] - {content.rid}"

def get_health_education_record_str(record):
    edu_time = parse_datetime_to_shanghai_time(record.edu_time)
    return f"[{HealthEducationWayEnum(record.way).label}({edu_time})] - {record.eid}"


class HealthEducationBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    
    
# 健康宣教内容列表
class HealthEducationListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_VIEW
    serializer_class = HealthEducationContentListSerializer
    response_msg = "获取健康宣教内容列表成功"
    error_response_msg = ""
    search_fields = ['title','keywords','category']
    audit_log_message = "健康宣教内容"
    
    def get_queryset(self):

        base_queryset = HealthEducationContent.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')

        formats = self.request.query_params.get('formats', None)
        
        print(f"formats: {formats}")
        
        if formats:
            
            if formats not in FormatEnum.values:
                self.error_response_msg = "无效的格式"
                return None
            
            base_queryset = base_queryset.filter(format=formats)
            
        return base_queryset

# 健康宣教内容创建
class HealthEducationCreateView(HealthEducationBaseView):
    
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def post(self, request):

        data = request.data.copy()

        data['maternity_center'] = request.user.maternity_center.id
        data['creator'] = request.user.id

        serializer = HealthEducationContentCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            if serializer.instance.attachment:
                AuditLogCreator.create_file_upload_audit_log(request,"健康宣教","上传了<健康宣教内容>的附件",{'model': 'HEC', 'rid': serializer.instance.rid, 'field': 'attachment'})
            AuditLogCreator.create_create_audit_log(request, "健康宣教内容", f"创建了{get_health_education_content_str(serializer.instance)}<健康宣教内容>")
            
            return make_response(code=0, msg="创建成功", data=HealehEducationContentDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="数据校验失败", data=serializer.errors)

# 健康宣教内容详情
class HealthEducationDetailView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_VIEW
    
    def get(self, request, rid):
            instance = HealthEducationContent.get_by_rid(rid,request.user.maternity_center)
            
            if not instance:
                return make_response(code=-1, msg="内容不存在")
            
            serializer = HealehEducationContentDetailSerializer(instance)
            
            AuditLogCreator.create_query_audit_log(request, "健康宣教内容", f"查看了{get_health_education_content_str(instance)}<健康宣教内容>详情")
            return make_response(code=0, msg="获取成功", data=serializer.data)

# 健康宣教内容更新
class HealthEducationUpdateView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def put(self, request, rid):
        
        instance = HealthEducationContent.get_by_rid(rid,request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="内容不存在")
        
        data = request.data.copy()
        
        serializer = HealthEducationContentUpdateSerializer(instance, data=data)
        if serializer.is_valid():
            cfs = get_field_changes(instance, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "健康宣教内容", f"更新了{get_health_education_content_str(instance)}<健康宣教内容>", cfs)
            return make_response(code=0, msg="更新成功", data=HealehEducationContentDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="数据校验失败", data=serializer.errors)

# 健康宣教内容删除
class HealthEducationDeleteView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def delete(self, request, rid):
        instance = HealthEducationContent.get_by_rid(rid,request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="内容不存在")
        
        AuditLogCreator.create_delete_audit_log(request, "健康宣教内容", f"删除了{get_health_education_content_str(instance)}<健康宣教内容>")
        instance.delete()
        
        return make_response(code=0, msg="删除成功")
    
    
    
    


# 健康宣教活动记录列表
class HealthEducationRecordListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_VIEW
    serializer_class = HealthEducationRecordListSerializer
    response_msg = "获取健康宣教活动记录列表成功"
    error_response_msg = ""
    search_fields = ['content__title','content__category','content__keywords']
    audit_log_message = "健康宣教活动记录"
    
    def get_queryset(self):

        base_queryset = HealthEducationRecord.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')

        app = self.request.query_params.get('app', None)
        apt = self.request.query_params.get('apt', None)
        executor = self.request.query_params.get('executor', None)
        way = self.request.query_params.get('way', None)
        
        if app:
            
            base_queryset = base_queryset.select_related('admission__maternity','admission__room').filter(Q(admission__maternity__name__icontains=app) | Q(admission__room__room_number__icontains=app))
                
        if apt:
            
            base_queryset = base_queryset.filter(edu_time__date=apt)
            
        if executor:
            
            base_queryset = base_queryset.filter(executor__icontains=executor)
            
        if way:
            
            base_queryset = base_queryset.filter(way=way)
            
        return base_queryset


# 健康宣教活动记录详情
class HealthEducationRecordDetailView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_VIEW
    
    def get(self, request, eid):
        instance = HealthEducationRecord.get_by_eid(eid,request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="记录不存在")
        
        serializer = HealehEducationRecordDetailSerializer(instance)
        
        AuditLogCreator.create_query_audit_log(request, "健康宣教活动记录", f"查看了{get_health_education_record_str(instance)}<健康宣教活动记录>详情")
        return make_response(code=0, msg="获取成功", data=serializer.data)
        


# 健康宣教活动记录创建
class HealthEducationRecordCreateView(HealthEducationBaseView):
    
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def post(self, request):

        data = request.data.copy()
        
        if data.get('content'):
            hel = HealthEducationContent.get_by_rid(data['content'],request.user.maternity_center)
            if not hel:
                return make_response(code=-1, msg="内容不存在")
            data['content'] = hel.id
            
        if data.get('aid'):
            admission = MaternityAdmission.get_maternity_admission_by_aid(data['aid'],request.user.maternity_center)
            if not admission:
                return make_response(code=-1, msg="入院记录不存在")
            data['admission'] = admission.id
        

        data['maternity_center'] = request.user.maternity_center.id
        data['creator'] = request.user.id
        

        serializer = HealthEducationRecordCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            AuditLogCreator.create_create_audit_log(request, "健康宣教活动记录", f"创建了{get_health_education_record_str(serializer.instance)}<健康宣教活动记录>")
            return make_response(code=0, msg="创建成功", data=HealehEducationRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="数据校验失败", data=serializer.errors)

# 健康宣教活动记录更新
class HealthEducationRecordUpdateView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def put(self, request, eid):
        
        instance = HealthEducationRecord.get_by_eid(eid,request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="记录不存在")
        
        data = request.data.copy()
        
        if data.get('content'):
            hel = HealthEducationContent.get_by_rid(data['content'],request.user.maternity_center)
            if not hel:
                return make_response(code=-1, msg="内容不存在")
            data['content'] = hel.id
            
        if data.get('way') and data.get('way') != HealthEducationWayEnum.ONE_ON_ONE_GUIDANCE:
            data['admission'] = None
        elif data.get('aid'):
            from customer_service.core_records.models.maternity_admission import MaternityAdmission
            admission = MaternityAdmission.get_maternity_admission_by_aid(data['aid'], request.user.maternity_center)
            if not admission:
                return make_response(code=-1, msg="入院记录不存在")
            data['admission'] = admission.id
        # 移除aid，避免传递给序列化器
        data.pop('aid', None)
        
        
        serializer = HealthEducationRecordUpdateSerializer(instance, data=data,partial=True)
        if serializer.is_valid():
            cfs = get_field_changes(instance, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "健康宣教活动记录", f"更新了{get_health_education_record_str(instance)}<健康宣教活动记录>", cfs)
            return make_response(code=0, msg="更新成功", data=HealehEducationRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="数据校验失败", data=serializer.errors)


# 健康宣教活动记录删除
class HealthEducationRecordDeleteView(HealthEducationBaseView):
    staff_required_permission = PermissionEnum.HEALTH_EDUCATION_EDIT
    
    def delete(self, request, eid):
        instance = HealthEducationRecord.get_by_eid(eid,request.user.maternity_center)
        
        if not instance:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        AuditLogCreator.create_delete_audit_log(request, "健康宣教活动记录", f"删除了{get_health_education_record_str(instance)}<健康宣教活动记录>")
        
        return make_response(code=0, msg="删除成功")
    
    

class InHouseMaternitySelectListView(PaginationListBaseView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = [PermissionEnum.HEALTH_EDUCATION_VIEW,PermissionEnum.HEALTH_EDUCATION_EDIT]
    serializer_class = MaternityAdmissionInHouseSelectListSerializer
    response_msg = "获取在住产妇列表成功"
    error_response_msg = "获取在住产妇列表失败"
    search_fields = ['maternity__name','maternity__phone','room__room_number']
    
    def get_queryset(self):
        return MaternityAdmission.objects.filter(maternity_center=self.request.user.maternity_center, check_in_status=CheckInStatusEnum.CHECKED_IN)