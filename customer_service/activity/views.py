from datetime import datetime, time

from django.utils import timezone
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLogCreator
from core.model import get_field_changes
from core.parse_time import parse_datetime_string, parse_datetime_to_shanghai_time
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.activity.enum import ActivityStatusEnum
from customer_service.activity.serializers import ActivityCreateSerializer, ActivityDetailSerializer, \
    ActivityListSerializer, ActivityUpdateSerializer
from file.models import ActivityCoverFile
from permissions.enum import PermissionEnum
from .models import Activity

def get_activity_str(activity):
    start_time = parse_datetime_to_shanghai_time(activity.start_time,format_str="%Y-%m-%d %H:%M")
    end_time = parse_datetime_to_shanghai_time(activity.end_time,format_str="%Y-%m-%d %H:%M")
    return f"[{activity.name}({start_time}至{end_time})] - {activity.rid}"

class ActivityBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]

# 活动列表
class ActivityListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_VIEW
    serializer_class = ActivityListSerializer
    response_msg = "获取活动列表成功"
    error_response_msg = "获取活动列表失败"
    search_fields = ['name', 'location', 'speaker','introduction','details','attention']
    audit_log_message = "活动"
    
    
    def get_queryset(self):

        aps = self.request.query_params.get('aps', None)
        atype = self.request.query_params.get('atype', None)
        ts = self.request.query_params.get('ts', None)
        
        base_queryset = Activity.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')

        if aps:
            now = timezone.now()

            if aps == ActivityStatusEnum.UPCOMING:
                base_queryset = base_queryset.filter(start_time__gt=now)
                
            elif aps == ActivityStatusEnum.IN_PROGRESS:
                base_queryset = base_queryset.filter(start_time__lte=now, end_time__gt=now)
                
            elif aps == ActivityStatusEnum.COMPLETED:
                base_queryset = base_queryset.filter(end_time__lte=now)
            else:
                self.error_response_msg = "活动状态不正确"
                return None

        if atype:
            base_queryset = base_queryset.filter(activity_type=atype)
            
        if ts:
            try:
                target_date = datetime.strptime(ts, '%Y-%m-%d').date()
                day_start = timezone.make_aware(datetime.combine(target_date, time.min))
                day_end = timezone.make_aware(datetime.combine(target_date, time.max))

                base_queryset = base_queryset.filter(
                    start_time__gte=day_start,
                    start_time__lte=day_end
                )
            except ValueError:
                self.error_response_msg = "日期格式不正确"
                return None

        return base_queryset



class ActivityTypeView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_VIEW

    def get(self, request):
        types = Activity.get_all_activity_types(request.user.maternity_center)
        return make_response(code=0, msg="获取活动类型成功", data=types)
    

# 活动创建
class ActivityCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_EDIT
    
    def post(self, request):
        data = request.data.copy()
        
        required_fields_map = {
            'name': '活动名称',
            'activity_type': '活动类型',
            'start_time': '开始时间',
            'end_time': '结束时间',
            'location': '活动地点',
            'speaker': '主讲人',
        }
        for field, msg in required_fields_map.items():
            if not data.get(field):
                return make_response(code=-1, msg=f"{msg}不能为空")
            
        if data.get('cover'):
            
            cover_file = ActivityCoverFile.get_activity_cover_file_by_rid(data.get('cover'),request.user.maternity_center)
            
            if not cover_file:
                return make_response(code=-1, msg="活动封面不存在")
            
        start_time = parse_datetime_string(data.get('start_time'))
        end_time = parse_datetime_string(data.get('end_time'))

        if not start_time or not end_time:
            return make_response(code=-1, msg="时间格式不正确")
        
        if start_time >= end_time:
            return make_response(code=-1, msg="开始时间不能大于或等于结束时间")
            
        data['maternity_center'] = request.user.maternity_center.id
        data['creator'] = request.user.id
        
        serializer = ActivityCreateSerializer(data=data)
        if serializer.is_valid():   
            serializer.save()
            AuditLogCreator.create_create_audit_log(request, "活动", f"创建了{get_activity_str(serializer.instance)}<活动>")
            return make_response(code=0, msg="活动创建成功", data= ActivityDetailSerializer(serializer.instance).data)
        
        return make_response(code=-1, msg="活动创建失败", data=serializer.errors)


# 活动详情
class ActivityDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_VIEW
    
    def get(self, request, rid):
        try:
            instance = Activity.objects.get(rid=rid, maternity_center=request.user.maternity_center)
            serializer = ActivityDetailSerializer(instance)
            AuditLogCreator.create_query_audit_log(request, "活动", f"查看了{get_activity_str(instance)}<活动>")
            return make_response(code=0, msg="获取成功", data=serializer.data)
        except Activity.DoesNotExist:
            return make_response(code=-1, msg="活动不存在")


# 活动更新
class ActivityUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_EDIT
    
    def put(self, request, rid):
        try:
            instance = Activity.objects.get(rid=rid, maternity_center=request.user.maternity_center)
            
            if instance.start_time < timezone.now() or instance.end_time < timezone.now():
                return make_response(code=-1, msg="活动已开始或结束，不允许更新活动信息")
            
            if request.data.get('cover'):
                
                cover_file = ActivityCoverFile.get_activity_cover_file_by_rid(request.data.get('cover'),request.user.maternity_center)
                if not cover_file:
                    return make_response(code=-1, msg="活动封面不存在")
            
            serializer = ActivityUpdateSerializer(instance, data=request.data, partial=True, context={'request': request})
            if serializer.is_valid():
                cfs = get_field_changes(instance,serializer.validated_data)
                serializer.save()
                AuditLogCreator.create_update_audit_log(request, "活动", f"更新了{get_activity_str(serializer.instance)}<活动>",cfs)
                return make_response(code=0, msg="活动更新成功", data=ActivityDetailSerializer(serializer.instance).data)
            return make_response(code=-1, msg="数据校验失败", data=serializer.errors)
        except Activity.DoesNotExist:
            return make_response(code=-1, msg="活动不存在")


# 活动删除
class ActivityDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ACTIVITY_EDIT
    
    def delete(self, request, rid):
        try:
            instance = Activity.objects.get(rid=rid, maternity_center=request.user.maternity_center)
            instance.delete()
            AuditLogCreator.create_delete_audit_log(request, "活动", f"删除了{get_activity_str(instance)}<活动>")
            return make_response(code=0, msg="删除成功")
        except Activity.DoesNotExist:
            return make_response(code=-1, msg="活动不存在")

