from dirtyfields import DirtyFieldsMixin
from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from hospital.models import Hospital
from maternity_center.models import MaternityCenter
from user.models import Staff


class Activity(BaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心")
    # 活动名称
    name = models.CharField(max_length=200, verbose_name="活动名称")
    # 活动类型
    activity_type = models.CharField(max_length=50, verbose_name="活动类型")
    # 开始时间
    start_time = models.DateTimeField(verbose_name="开始时间")
    # 结束时间
    end_time = models.DateTimeField(verbose_name="结束时间")
    # 活动地点
    location = models.CharField(max_length=500, verbose_name="活动地点")
    # 主讲人
    speaker = models.CharField(max_length=100, verbose_name="主讲人")
    # 活动封面,字符串类型
    cover = models.CharField(max_length=500, verbose_name="活动封面",blank=True,default="")
    # 活动简介
    introduction = models.TextField( verbose_name="活动简介",blank=True,default="")
    # 活动详情
    details = models.TextField(verbose_name="活动详情",blank=True,default="")
    # 注意事项
    attention = models.TextField(verbose_name="注意事项",blank=True,default="")
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人",null=True,blank=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="标识符",default=generate_resource_uuid)

    class Meta:
        verbose_name = "活动"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name
    
    
    @classmethod
    def get_all_activity_types(cls, maternity_center):

        if not maternity_center:
            return []

        return list(set(cls.objects.filter(maternity_center=maternity_center).values_list('activity_type', flat=True)))
    