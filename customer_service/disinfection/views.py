from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.logs import AuditLog<PERSON>reator
from core.model import get_field_changes
from core.parse_time import parse_datetime_to_shanghai_time
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.disinfection.enum import DisinfectionDocumentStatusEnum
from customer_service.disinfection.models import CleanDisinfectionRecord, DisinfectionDocument
from customer_service.disinfection.serializers import CleanDisinfectionRecordCreateSerializer, \
    CleanDisinfectionRecordDetailSerializer, CleanDisinfectionRecordListSerializer, \
    CleanDisinfectionRecordUpdateSerializer, DisinfectionDocumentCreateSerializer, DisinfectionDocumentDetailSerializer, \
    DisinfectionDocumentListSerializer, DisinfectionDocumentUpdateSerializer
from file.models import DisinfectFile
from permissions.enum import PermissionEnum


def get_disinfection_document_str(document):
    return f"[{document.name}] - {document.rid}"

def get_clean_disinfection_record_str(record):
    clean_time = parse_datetime_to_shanghai_time(record.clean_time)
    return f"[{record.clean_area}({clean_time})] - {record.rid}"


# 消毒规范文库列表
class DocumentListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    serializer_class = DisinfectionDocumentListSerializer
    response_msg = "获取消毒规范文库列表成功"
    error_response_msg = ""
    search_fields = ["name",'applicable_scope']
    audit_log_message = "消毒规范文库"
    
    def get_queryset(self):
        
        base_queryset = DisinfectionDocument.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        publish_date = self.request.query_params.get('publish_date', None)
        
        if publish_date:
            base_queryset = base_queryset.filter(publish_date=publish_date)
            
        return base_queryset

# 消毒规范文库详情
class DocumentDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    
    def get(self,request,rid):
        
        disinfection_document = DisinfectionDocument.get_disinfection_document_by_rid(rid,request.user.maternity_center)
        
        if not disinfection_document:
            return make_response(code=-1,msg="消毒规范文库不存在")
        
        serializer = DisinfectionDocumentDetailSerializer(disinfection_document)
        
        AuditLogCreator.create_query_audit_log(request, "消毒规范文库", f"查看了{get_disinfection_document_str(disinfection_document)}<消毒规范文库>详情")
        return make_response(code=0,msg="获取消毒规范文库详情成功",data=serializer.data)
    
    

# 创建消毒规范文库
class DocumentCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        status = data.get("status")
        
        file = data.get("file")
        if file:
            file = DisinfectFile.get_by_rid(file,request.user.maternity_center)
            if not file:
                return make_response(code=-1,msg="文件不存在")
        
        
        if status and status not in DisinfectionDocumentStatusEnum.values:
            return make_response(code=-1,msg="文档状态不正确")
        
        data["maternity_center"] = request.user.maternity_center.id
        data["creator"] = request.user.id
        
        serializer = DisinfectionDocumentCreateSerializer(data=data)
        
        if serializer.is_valid():
            serializer.save()
            AuditLogCreator.create_create_audit_log(request, "消毒规范文库", f"创建了{get_disinfection_document_str(serializer.instance)}<消毒规范文库>")
            return make_response(code=0,msg="创建消毒规范文库成功",data=DisinfectionDocumentDetailSerializer(serializer.instance).data)
        
        return make_response(code=-1,msg="创建消毒规范文库失败",data=serializer.errors)
    
# 更新消毒规范文库
class DocumentUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT

    def put(self,request,rid):
        
        data = request.data.copy()
        
        disinfection_document = DisinfectionDocument.get_disinfection_document_by_rid(rid,request.user.maternity_center)
        
        if not disinfection_document:
            return make_response(code=-1,msg="消毒规范文库不存在")
        
        file = data.get("file")
        if file:
            file = DisinfectFile.get_by_rid(file,request.user.maternity_center)
            if not file:
                return make_response(code=-1,msg="文件不存在")
        
        serializer = DisinfectionDocumentUpdateSerializer(disinfection_document,data=data)
        
        if serializer.is_valid():
            cfs = get_field_changes(disinfection_document, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "消毒规范文库", f"更新了{get_disinfection_document_str(disinfection_document)}<消毒规范文库>", cfs)
            return make_response(code=0,msg="更新消毒规范文库成功",data=DisinfectionDocumentDetailSerializer(serializer.instance).data)
        
        return make_response(code=-1,msg="更新消毒规范文库失败",data=serializer.errors)
    
    
# 删除消毒规范文库
class DocumentDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def delete(self,request,rid):

        disinfection_document = DisinfectionDocument.get_disinfection_document_by_rid(rid,request.user.maternity_center)
        
        if not disinfection_document:
            return make_response(code=-1,msg="消毒规范文库不存在")
        
        AuditLogCreator.create_delete_audit_log(request, "消毒规范文库", f"删除了{get_disinfection_document_str(disinfection_document)}<消毒规范文库>")
        disinfection_document.delete()
        
        return make_response(code=0,msg="删除消毒规范文库成功") 
    
    
    
    
    
    
    
    
    

# 清洁消毒记录列表
class CleanDisinfectionRecordListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    serializer_class = CleanDisinfectionRecordListSerializer
    response_msg = "获取清洁消毒记录列表成功"
    error_response_msg = ""
    search_fields = ['clean_area','clean_type','disinfection_document__name','cleaner','supervisor']
    audit_log_message = "清洁消毒记录"
    
    def get_queryset(self):
        
        base_queryset = CleanDisinfectionRecord.objects.filter(maternity_center=self.request.user.maternity_center)
        
        clean_area = self.request.query_params.get('clean_area')
        clean_type = self.request.query_params.get('clean_type')
        disinfection_document = self.request.query_params.get('disinfection_document')
        
        if clean_area:
            base_queryset = base_queryset.filter(clean_area=clean_area)

        if clean_type:
            base_queryset = base_queryset.filter(clean_type=clean_type)
        
        if disinfection_document:
            base_queryset = base_queryset.filter(disinfection_document__name__icontains=disinfection_document)
        
        return base_queryset
    

# 清洁消毒记录详情
class CleanDisinfectionRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_VIEW
    
    def get(self,request,rid):
        
        clean_disinfection_record = CleanDisinfectionRecord.get_clean_disinfection_record_by_rid(rid,request.user.maternity_center)
        
        if not clean_disinfection_record:
            return make_response(code=-1,msg="清洁消毒记录不存在")
        
        serializer = CleanDisinfectionRecordDetailSerializer(clean_disinfection_record)
        
        AuditLogCreator.create_query_audit_log(request, "清洁消毒记录", f"查看了{get_clean_disinfection_record_str(clean_disinfection_record)}<清洁消毒记录>详情")
        return make_response(code=0,msg="获取清洁消毒记录详情成功",data=serializer.data)
    
    

# 创建清洁消毒记录
class CleanDisinfectionRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data["maternity_center"] = request.user.maternity_center.id
        data["creator"] = request.user.id
        
        serializer = CleanDisinfectionRecordCreateSerializer(data=data,context={"request":request})
        
        if serializer.is_valid():
            serializer.save()
            AuditLogCreator.create_create_audit_log(request, "清洁消毒记录", f"创建了{get_clean_disinfection_record_str(serializer.instance)}<清洁消毒记录>")
            return make_response(code=0,msg="创建清洁消毒记录成功",data=CleanDisinfectionRecordDetailSerializer(serializer.instance).data)
        
        return make_response(code=-1,msg="创建清洁消毒记录失败",data=serializer.errors)
    
# 更新清洁消毒记录
class CleanDisinfectionRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT

    def put(self,request,rid):
        
        data = request.data.copy()
        
        clean_disinfection_record = CleanDisinfectionRecord.get_clean_disinfection_record_by_rid(rid,request.user.maternity_center)
        
        if not clean_disinfection_record:
            return make_response(code=-1,msg="清洁消毒记录不存在")
        
        
        serializer = CleanDisinfectionRecordUpdateSerializer(clean_disinfection_record,data=data,context={"request":request})
        
        if serializer.is_valid():
            cfs = get_field_changes(clean_disinfection_record, serializer.validated_data)
            serializer.save()
            AuditLogCreator.create_update_audit_log(request, "清洁消毒记录", f"更新了{get_clean_disinfection_record_str(clean_disinfection_record)}<清洁消毒记录>", cfs)
            return make_response(code=0,msg="更新清洁消毒记录成功",data=CleanDisinfectionRecordDetailSerializer(serializer.instance).data)
        
        return make_response(code=-1,msg="更新清洁消毒记录失败",data=serializer.errors)
    
    
# 删除清洁消毒记录
class CleanDisinfectionRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.INFECTION_CONTROL_AND_ENVIRONMENT_EDIT
    
    def delete(self,request,rid):

        clean_disinfection_record = CleanDisinfectionRecord.get_clean_disinfection_record_by_rid(rid,request.user.maternity_center)
        
        if not clean_disinfection_record:
            return make_response(code=-1,msg="清洁消毒记录不存在")
        
        clean_disinfection_record.delete()
        AuditLogCreator.create_delete_audit_log(request, "清洁消毒记录", f"删除了{get_clean_disinfection_record_str(clean_disinfection_record)}<清洁消毒记录>")
        
        return make_response(code=0,msg="删除清洁消毒记录成功") 