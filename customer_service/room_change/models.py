from dirtyfields import DirtyFieldsMixin
from django.db import models
from django.db import transaction
from django.utils import timezone

from core.enum import ApprovalStatusEnum, CheckInStatusEnum
from core.generate_hashid import generate_room_change_application_code
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.room.enum import RoomStatusEnum
from customer_service.room.models import Room
from maternity_center.models import MaternityCenter
from user.models import Staff


# 换房申请单
class RoomChangeApplication(BaseModel, DirtyFieldsMixin):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='room_change_applications')
    # 入住单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入住单",related_name='room_change_applications')
    # 意向房间
    intented_room = models.ForeignKey(Room, on_delete=models.CASCADE, verbose_name="意向房间",related_name='room_change_applications')
    # 换房原因
    reason = models.TextField(verbose_name="换房原因",blank=True,default='')
    # 费用差异说明
    price_difference_description = models.TextField(verbose_name="费用差异说明",blank=True,default='')
    # 费用差额
    price_difference = models.DecimalField(verbose_name="费用差额",max_digits=10,decimal_places=2,default=0)
    # 申请时间
    application_time = models.DateTimeField(verbose_name="申请时间",auto_now_add=True)
    # 申请状态
    approval_status = models.CharField(verbose_name="申请状态",max_length=20,choices=ApprovalStatusEnum.choices,default=ApprovalStatusEnum.PENDING)
    # 审核时间
    audit_time = models.DateTimeField(verbose_name="审核时间",null=True,blank=True)
    # 审核人
    audit_by = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="审核人",related_name='room_change_applications',null=True,blank=True)
    # 审核意见
    audit_opinion = models.TextField(verbose_name="审核意见",blank=True,default='')
    # 换房申请单号
    rid = models.CharField(verbose_name="换房申请单号",max_length=20,blank=True,unique=True,default=generate_room_change_application_code)
    
    class Meta:
        verbose_name = "换房申请单"
        verbose_name_plural = "换房申请单"
    
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} - {self.application_time}"
    
    @staticmethod
    def create_room_change_application(maternity_center, maternity_admission, intented_room, reason, price_difference_description, price_difference):
        with transaction.atomic():
            try:
                room = Room.objects.get(id=intented_room.id,maternity_center=maternity_center)
                existing_admissions = MaternityAdmission.objects.filter(
                room=room,
                check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN])
                result,error_msg = room.check_room_available(maternity_admission.expected_check_out_date,existing_admissions)
                if not result:
                    return None,error_msg
                room.room_status = RoomStatusEnum.UNAVAILABLE_SWITCH_ROOM
                room.save()
            except Room.DoesNotExist:
                return None,f"意向房间不存在"
            
            room_change_application = RoomChangeApplication.objects.create(
                maternity_center=maternity_center,
                maternity_admission=maternity_admission,
                intented_room=intented_room,
                reason=reason,
                price_difference_description=price_difference_description,
                price_difference=price_difference)
            
            return room_change_application,None


    @staticmethod
    def has_pending_room_change(maternity_admission):
        return RoomChangeApplication.objects.filter(maternity_admission=maternity_admission, approval_status=ApprovalStatusEnum.PENDING).exists()
    
    def audit_room_change_application(self, result,staff,opinion):
        
        if result:
            self.maternity_admission.room = self.intented_room
            self.maternity_admission.save()
        
        self.intented_room.update_status(RoomStatusEnum.AVAILABLE,reason=f"换房申请{'通过' if result else '未通过'}，房间恢复可用")
        
        self.approval_status = ApprovalStatusEnum.APPROVED if result else ApprovalStatusEnum.REJECTED
        self.audit_time = timezone.now()
        self.audit_by = staff
        self.audit_opinion = opinion
        self.save()
        return self