from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation

from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.enum import ApprovalStatusEnum, CheckInStatusEnum
from core.logs import <PERSON>tLogC<PERSON>
from core.model import get_field_changes
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.room.enum import RoomStatusEnum
from customer_service.room.models import Room
from customer_service.room_change.models import RoomChangeApplication
from customer_service.room_change.serializers import RoomChangeApplicationDetailSerializer, \
    RoomChangeApplicationSerializer, RoomChangeApplicationUpdateSerializer
from permissions.enum import PermissionEnum


def get_room_change_application_str(application):
    return f"[{application.maternity_admission.maternity.name} 换房至{application.intented_room.room_number}] - {application.rid}"


class RoomChangeApplicationBaseView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]


class RoomChangeApplicationListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.ROOM_CHANGE_VIEW 
    
    serializer_class = RoomChangeApplicationSerializer
    response_msg = "获取外出申请列表成功"
    error_response_msg = ""
    search_fields = ["maternity_admission__maternity__name","maternity_admission__room__room_number"]
    audit_log_message = "换房申请"
    
    def get_queryset(self):
        
        aps = self.request.query_params.get('aps', None)
        apt = self.request.query_params.get('apt', None)
        
        base_queryset = RoomChangeApplication.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-application_time')
        
        if aps:
            if aps not in ApprovalStatusEnum.values:
                self.error_response_msg = "审核状态不正确"
                return None
            base_queryset = base_queryset.filter(approval_status=aps)
            
            
        if apt:
            try:
                apt_datetime = datetime.strptime(apt, "%Y-%m-%d %H:%M:%S")
                base_queryset = base_queryset.filter(application_time__gte=apt_datetime)
            except ValueError:
                self.error_response_msg = "查询日期格式错误"
                return None
                
        return base_queryset
    
class RoomChangeApplicationDetailView(RoomChangeApplicationBaseView):
    
    staff_required_permission = PermissionEnum.ROOM_CHANGE_VIEW
    
    def get(self, request, rid):
        try:
            room_change_application = RoomChangeApplication.objects.select_related(
                'maternity_admission__maternity','intented_room'
            ).get(rid=rid,maternity_center=request.user.maternity_center)
            serializer = RoomChangeApplicationDetailSerializer(room_change_application)
            AuditLogCreator.create_query_audit_log(request, "换房申请", f"查看了{get_room_change_application_str(room_change_application)}<换房申请>详情")
            return make_response(code=0, msg="获取换房申请详情成功", data=serializer.data)
        except RoomChangeApplication.DoesNotExist:
            return make_response(code=-1, msg="外出申请单不存在")
    
class RoomChangeApplicationCreateView(RoomChangeApplicationBaseView):
    
    staff_required_permission = PermissionEnum.ROOM_CHANGE_EDIT

    def post(self, request, aid):

        # 获取请求参数
        intented_room_rid = request.data.get("intented_room", None)
        reason = request.data.get("reason", "")
        price_difference_description = request.data.get("price_difference_description", "")
        price_difference_raw = request.data.get("price_difference", None)


        if intented_room_rid is None:
            return make_response(code=-1, msg="请提供意向房间ID")

        if price_difference_raw is None:
            return make_response(code=-1, msg="请提供费用差额")

        try:
            price_difference = Decimal(str(price_difference_raw)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        except (ValueError, TypeError, InvalidOperation):
            return make_response(code=-1, msg="费用差额格式不正确，请输入有效数字")


        maternity_admission = MaternityAdmission.get_maternity_admission_by_aid(aid, request.user.maternity_center)
        if not maternity_admission:
            return make_response(code=-1, msg="产妇入院记录不存在")

        intented_room = Room.get_room_by_rid(rid=intented_room_rid,maternity_center=request.user.maternity_center)
        if not intented_room:
            return make_response(code=-1, msg="意向房间不存在")


        if RoomChangeApplication.objects.filter(
            maternity_admission=maternity_admission,
            approval_status=ApprovalStatusEnum.PENDING
        ).exists():
            return make_response(code=-1, msg="该产妇已有待审核的换房申请，请等待审核完成后再申请")

        # 检查是否换到同一房间
        if maternity_admission.room and maternity_admission.room.id == intented_room.id:
            return make_response(code=-1, msg="意向房间与当前房间相同，无需换房")
        
        cra , error_msg = RoomChangeApplication.create_room_change_application(request.user.maternity_center,maternity_admission,intented_room,reason,price_difference_description,price_difference)
        if not cra and error_msg:
            return make_response(code=-1, msg=error_msg)
        
        serializer = RoomChangeApplicationDetailSerializer(cra)
        AuditLogCreator.create_create_audit_log(request, "换房申请", f"创建了{get_room_change_application_str(cra)}<换房申请>")
        return make_response(code=0, msg="创建换房申请成功", data=serializer.data)
        
        
        

class RoomChangeApplicationUpdateView(RoomChangeApplicationBaseView):
    
    staff_required_permission = PermissionEnum.ROOM_CHANGE_EDIT

    def put(self, request, rid):
        
        irid = request.data.get("intented_room", 0)
        
        # 获取换房申请单
        try:
            room_change_application = RoomChangeApplication.objects.select_related(
                'maternity_admission__maternity','intented_room'
            ).get(rid=rid,maternity_center=request.user.maternity_center)
        except RoomChangeApplication.DoesNotExist:
            return make_response(code=-1, msg="换房申请单不存在")
        
        if room_change_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="换房申请单已审核，无法修改")
        
        ir = Room.get_room_by_rid(rid=irid,maternity_center=request.user.maternity_center)
        if not ir:
            return make_response(code=-1, msg="数据校验失败，意向房间不存在")
        
        existing_admissions = MaternityAdmission.objects.filter(
                room=ir,
                check_in_status__in=[CheckInStatusEnum.RESERVED, CheckInStatusEnum.CHECKED_IN])
        
        if irid != room_change_application.intented_room.id:
            result,error_msg = ir.check_room_available(room_change_application.maternity_admission.expected_check_out_date,existing_admissions)
            if not result:
                return make_response(code=-1, msg=error_msg)
        
        serializer = RoomChangeApplicationUpdateSerializer(room_change_application, data=request.data)
        if serializer.is_valid():
            cfs = get_field_changes(room_change_application, serializer.validated_data)
            serializer.save()
            resData = RoomChangeApplicationDetailSerializer(serializer.instance).data
            AuditLogCreator.create_update_audit_log(request, "换房申请", f"更新了{get_room_change_application_str(room_change_application)}<换房申请>", cfs)
            return make_response(code=0, msg="更新换房申请成功", data=resData)
        else:
            return make_response(code=-1, msg=serializer.errors)


class RoomChangeApplicationDeleteView(RoomChangeApplicationBaseView):
    
    staff_required_permission = PermissionEnum.ROOM_CHANGE_EDIT

    def delete(self, request, rid):
        try:
            room_change_application = RoomChangeApplication.objects.select_related(
                'maternity_admission__maternity','intented_room'
            ).get(rid=rid,maternity_center=request.user.maternity_center)
        except RoomChangeApplication.DoesNotExist:
            return make_response(code=-1, msg="换房申请单不存在")
        
        if room_change_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="换房申请单已审核，无法删除，审核结果为：%s" % ApprovalStatusEnum(room_change_application.approval_status).label)
        
        room_change_application.intented_room.update_status(RoomStatusEnum.AVAILABLE,reason="换房申请取消，房间恢复可用")
        room_change_application.delete()
        AuditLogCreator.create_delete_audit_log(request, "换房申请", f"删除了{get_room_change_application_str(room_change_application)}<换房申请>")
        return make_response(code=0, msg="删除换房申请成功")

class RoomChangeApplicationAuditView(RoomChangeApplicationBaseView):
    
    staff_required_permission = PermissionEnum.ROOM_CHANGE_AUDIT

    def put(self, request, rid):
        
        result = request.data.get("result", None)
        opinion = request.data.get("opinion", "")
        
        if result not in [True, False]:
            return make_response(code=-1, msg="数据校验失败，审核结果不正确或未提供审核结果")
        
        try:
            room_change_application = RoomChangeApplication.objects.select_related(
                'maternity_admission__maternity','intented_room'
            ).get(rid=rid,maternity_center=request.user.maternity_center)
        except RoomChangeApplication.DoesNotExist:
            return make_response(code=-1, msg="换房申请单不存在")
        
        if room_change_application.approval_status != ApprovalStatusEnum.PENDING:
            return make_response(code=-1, msg="换房申请单已审核，无法再次审核，审核结果为：%s" % ApprovalStatusEnum(room_change_application.approval_status).label)
        
        room_change_application.audit_room_change_application(result,request.user,opinion)
        
        audit_result_text = "通过" if result else "拒绝"
        AuditLogCreator.create_request_audit_log(request, "换房申请", f"审核了{get_room_change_application_str(room_change_application)}<换房申请>，审核结果：{audit_result_text}")
        
        resData = RoomChangeApplicationDetailSerializer(room_change_application).data
        return make_response(code=0, msg="换房申请审核成功", data=resData)