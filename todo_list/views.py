from rest_framework.views import APIView
from core.authorization import CareCenterAuthentication, HasStaffPermission, StaffWithSpecificPermissionOnly
from core.enum import CheckInStatusEnum
from core.parse_time import parse_datetime_string
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from message.utils import send_todo_list_message
from permissions.enum import PermissionEnum
from todo_list.enum import TodoListStatusEnum, TodoListTypeEnum
from todo_list.models import TodoList
from todo_list.serializers import MaternitySelectListSerializer, StaffSerializer, AssignTodoAssignCreateSerializer, AssignTodoAssignUpdateSerializer, AssignTodoDetailSerializer, AssignTodoListSerializer, StaffTodoDetailSerializer, StaffTodoListSerializer
from user.models import Staff


# 待办事项列表
class TodoListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]
    serializer_class = StaffTodoListSerializer
    response_msg = "获取待办事项列表成功"
    error_response_msg = "获取待办事项列表失败"
    search_fields = ['maternity_admission__maternity__name','maternity_admission__room__room_number','todo_content','todo_remark']

    def get_queryset(self):
        
        base_queryset = TodoList.get_list_by_assigned_to(self.request.user).order_by('-created_at')
        
        todo_status = self.request.query_params.get('todo_status',None)
        todo_type = self.request.query_params.get('todo_type',None)
        
        if todo_status and todo_status in TodoListStatusEnum.values:
            base_queryset = base_queryset.filter(todo_status=todo_status)
        
        if todo_type and todo_type in TodoListTypeEnum.values:
            base_queryset = base_queryset.filter(todo_type=todo_type)
        
        return base_queryset
    
    
# 待办事项详情
class TodoDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]
    
    def get(self, request, rid):
        
        todo = TodoList.get_assigned_by_rid(rid,request.user)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        todo.mark_as_in_progress()
        
        serializer = StaffTodoDetailSerializer(todo)
        
        return make_response(code=0,msg='获取待办事项详情成功',data=serializer.data)
    
    
# 完成待办
class TodoCompleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [HasStaffPermission]
    
    def put(self, request, rid):

        todo = TodoList.get_assigned_by_rid(rid,request.user)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")

        if todo.todo_status != TodoListStatusEnum.IN_PROGRESS:
            return make_response(code=-1,msg="待办事项状态不正确")

        data = request.data.copy()
        
        complete_time_str = data.get('complete_time',None)
        
        complete_time = parse_datetime_string(complete_time_str)
        
        if not complete_time:
            return make_response(code=-1,msg="完成时间错误")
        
        complete_feedback = data.get('complete_feedback',"")
        
        if not complete_feedback:
            return make_response(code=-1,msg="完成反馈不能为空")
        
        todo.mark_as_completed(complete_time,complete_feedback)
        
        return make_response(code=0,msg='完成待办事项成功',data=StaffTodoDetailSerializer(todo).data)


# 待办事项列表
class TodoAssignListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    serializer_class = AssignTodoListSerializer
    response_msg = "获取待办事项列表成功"
    search_fields = ['assign__name','assigned_to__name','maternity_admission__maternity__name','maternity_admission__room__room_number']
    
    def get_queryset(self):
        
        base_queryset = TodoList.get_list_by_maternity_center(self.request.user.maternity_center).order_by('-created_at')
        
        todo_status = self.request.query_params.get('todo_status',None)
        todo_type = self.request.query_params.get('todo_type',None)
        
        if todo_status and todo_status in TodoListStatusEnum.values:
            base_queryset = base_queryset.filter(todo_status=todo_status)
        
        if todo_type and todo_type in TodoListTypeEnum.values:
            base_queryset = base_queryset.filter(todo_type=todo_type)
        
        return base_queryset



# 待办事项详情
class TodoAssignDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def get(self, request, rid):
        
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        return make_response(code=0,msg='获取待办事项详情成功',data=AssignTodoDetailSerializer(todo,context={'request':request}).data)



# 创建待办事项
class TodoAssignCreateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def post(self, request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        data['assign'] = request.user.id

        serializer = AssignTodoAssignCreateSerializer(data=data,context={'maternity_center':request.user.maternity_center})
        if serializer.is_valid():
            serializer.save()
            
            send_todo_list_message(serializer.instance.assigned_to.phone,{
                'brand_name':request.user.maternity_center.name,
                'todo_name':TodoListTypeEnum(serializer.instance.todo_type).label,
            })
            
            return make_response(code=0,msg='创建待办事项成功',data=AssignTodoDetailSerializer(serializer.instance,context={'request':request}).data)
        else:
            return make_response(code=-1,msg='创建待办事项失败',data=serializer.errors)
        

# 更新待办事项
class TodoAssignUpdateView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def put(self, request, rid):
        
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        if todo.assign != request.user:
            return make_response(code=-1,msg="您没有权限更新此待办事项")
        
        data = request.data.copy()
                
        serializer = AssignTodoAssignUpdateSerializer(todo,data=data,context={'maternity_center':request.user.maternity_center})
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='更新待办事项成功',data=AssignTodoDetailSerializer(serializer.instance,context={'request':request}).data)
        else:
            return make_response(code=-1,msg='更新待办事项失败')
        

# 删除待办事项  
class TodoAssignDeleteView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def delete(self, request, rid):
        todo = TodoList.get_by_rid(rid,request.user.maternity_center)
        
        if not todo:
            return make_response(code=-1,msg="待办事项不存在")
        
        todo.delete()
        
        return make_response(code=0,msg='删除待办事项成功')
    
    
    
    
    
# 员工列表
class StaffListView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT

    
    def get(self, request):
        return make_response(code=0,msg='获取员工列表成功',data=StaffSerializer(Staff.objects.filter(is_active=True),many=True).data)
    
    
    

# 产妇列表
class MaternityListView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.TODO_LIST_ASSIGN_EDIT
    
    def get(self,request):
        
        queryset = MaternityAdmission.objects.filter(maternity_center=request.user.maternity_center,check_in_status=CheckInStatusEnum.CHECKED_IN).order_by('-actual_check_in_date')
        
        return make_response(code=0,msg='获取在住产妇选择列表成功',data=MaternitySelectListSerializer(queryset,many=True).data)