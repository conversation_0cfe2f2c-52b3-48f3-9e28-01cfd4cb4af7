from django.urls import path

from permissions.views import RoleCreateView
from todo_list.views import MaternityListView, StaffListView, TodoAssignCreateView, TodoAssignDeleteView, TodoAssignDetailView, TodoAssignListView, TodoAssignUpdateView, TodoCompleteView, TodoDetailView, TodoListView


urlpatterns = [

    # 待办事项列表
    path('list/', TodoListView.as_view(), name='todo-list'),
    # 待办事项详情
    path('detail/<str:rid>/', TodoDetailView.as_view(), name='todo-detail'),
    # 完成待办
    path('complete/<str:rid>/', TodoCompleteView.as_view(), name='todo-complete'),
    
    
    
    
    
    
    
    # 待办事项列表
    path('assign/list/', TodoAssignListView.as_view(), name='todo-assign-list'),
    # 待办事项详情
    path('assign/detail/<str:rid>/', TodoAssignDetailView.as_view(), name='todo-assign-detail'),
    # 创建待办事项
    path('assign/create/', TodoAssignCreateView.as_view(), name='todo-assign-create'),
    # 更新待办事项
    path('assign/update/<str:rid>/', TodoAssignUpdateView.as_view(), name='todo-assign-update'),
    # 删除待办事项
    path('assign/delete/<str:rid>/', TodoAssignDeleteView.as_view(), name='todo-assign-delete'),
    
    # 员工列表
    path('staff/list/', StaffListView.as_view(), name='staff-list'),
    # 产妇列表
    path('maternity/list/', MaternityListView.as_view(), name='maternity-list'),
]

