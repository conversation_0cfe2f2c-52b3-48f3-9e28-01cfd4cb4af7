from rest_framework import serializers
from core.enum import CheckInStatusEnum
from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from todo_list.enum import TodoListStatusEnum
from todo_list.models import TodoList
from user.models import Staff



# 员工待办事项列表
class StaffTodoListSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 指派人
    assign_name = serializers.SerializerMethodField()
    # 执行人
    assigned_to_name = serializers.SerializerMethodField()
    # 完成时间
    complete_time = ShanghaiFriendlyDateTimeField()
    # 事项类型
    todo_type_display = serializers.SerializerMethodField()
    # 状态
    todo_status_display = serializers.SerializerMethodField()
    # 产妇信息
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = TodoList
        fields = [
            'rid',
            'created_at',
            'assign_name',
            'assigned_to_name',
            'maternity',
            'todo_status',
            'todo_status_display',
            'todo_type',
            'todo_type_display',
            'complete_time',
            'todo_content'
            ]
        
    def get_assign_name(self,obj):
        if not obj.assign:
            return '-'
        
        return obj.assign.name
    
    def get_assigned_to_name(self,obj):
        if not obj.assigned_to:
            return '-'
        
        return obj.assigned_to.name 

    def get_maternity(self,obj):
        if not obj.maternity_admission:
            return '-'
        
        name = obj.maternity_admission.maternity.name or '-'
        room_number = obj.maternity_admission.room.room_number or '-'
        return f"{name} - ({room_number})"

    def get_todo_type_display(self,obj):
        return obj.get_todo_type_display()
    
    def get_todo_status_display(self,obj):
        return obj.get_todo_status_display()
    



# 员工待办事项详情
class StaffTodoDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 完成时间
    complete_time = ShanghaiFriendlyDateTimeField()
    # 事项类型
    todo_type_display = serializers.SerializerMethodField()
    # 状态
    todo_status_display = serializers.SerializerMethodField()
    # 产妇信息
    maternity = serializers.SerializerMethodField()
    # 入院单
    maternity_admission = serializers.SerializerMethodField()
    # 指派人
    assign_name = serializers.SerializerMethodField()
    class Meta:
        model = TodoList
        fields = [
            'rid',
            'todo_type',
            'todo_type_display',
            'todo_content',
            'todo_remark',
            'todo_status',
            'todo_status_display',
            'complete_feedback',
            'complete_time',
            'maternity_admission',
            'maternity',
            'created_at',
            'updated_at',
            'assign_name',
            ]
        
    def get_todo_type_display(self,obj):
        return obj.get_todo_type_display()
    
    def get_todo_status_display(self,obj):
        return obj.get_todo_status_display()
    
    def get_maternity(self,obj):
        if not obj.maternity_admission:
            return '-'
        
        name = obj.maternity_admission.maternity.name or '-'
        room_number = obj.maternity_admission.room.room_number or '-'
        return f"{name} - ({room_number})"
    
    def get_maternity_admission(self,obj):
        if not obj.maternity_admission:
            return None
        
        return obj.maternity_admission.aid
    
    def get_assign_name(self,obj):
        if not obj.assign:
            return '-'
        
        return obj.assign.name
    








# 管理待办事项列表
class AssignTodoListSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 指派人
    assign_name = serializers.SerializerMethodField()
    # 执行人
    assigned_to_name = serializers.SerializerMethodField()
    # 完成时间
    complete_time = ShanghaiFriendlyDateTimeField()
    # 事项类型
    todo_type_display = serializers.SerializerMethodField()
    # 状态
    todo_status_display = serializers.SerializerMethodField()
    # 是否可以编辑
    is_editable = serializers.SerializerMethodField()
    # 产妇信息
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = TodoList
        fields = [
            'rid',
            'created_at',
            'assign_name',
            'assigned_to_name',
            'maternity',
            'todo_status',
            'todo_status_display',
            'todo_type',
            'todo_type_display',
            'complete_time',
            'is_editable',
            'todo_content'
            ]
        
    def get_assign_name(self,obj):
        if not obj.assign:
            return '-'
        
        return obj.assign.name
    
    def get_assigned_to_name(self,obj):
        if not obj.assigned_to:
            return '-'
        
        return obj.assigned_to.name 

    def get_maternity(self,obj):
        if not obj.maternity_admission:
            return '-'
        
        name = obj.maternity_admission.maternity.name or '-'
        room_number = obj.maternity_admission.room.room_number or '-'
        return f"{name} - ({room_number})"

    def get_todo_type_display(self,obj):
        return obj.get_todo_type_display()
    
    def get_todo_status_display(self,obj):
        return obj.get_todo_status_display()
    
    def get_is_editable(self,obj):
        return (obj.todo_status != TodoListStatusEnum.COMPLETED and obj.assign == self.context['request'].user)
    
# 管理待办事项详情
class AssignTodoDetailSerializer(serializers.ModelSerializer):
    
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 完成时间
    complete_time = ShanghaiFriendlyDateTimeField()
    # 事项类型
    todo_type_display = serializers.SerializerMethodField()
    # 状态
    todo_status_display = serializers.SerializerMethodField()
    # 是否可以编辑
    is_editable = serializers.SerializerMethodField()
    # 产妇信息
    maternity = serializers.SerializerMethodField()
    # 入院单
    maternity_admission = serializers.SerializerMethodField()
    # 指派人
    assign_name = serializers.SerializerMethodField()
    # 执行人 sid
    assigned_to = serializers.SerializerMethodField()
    # 执行人
    assigned_to_name = serializers.SerializerMethodField()
    class Meta:
        model = TodoList
        fields = [
            'rid',
            'todo_type',
            'todo_type_display',
            'todo_content',
            'todo_remark',
            'todo_status',
            'todo_status_display',
            'complete_feedback',
            'complete_time',
            'maternity_admission',
            'maternity',
            'created_at',
            'updated_at',
            'is_editable',
            'assign_name',
            'assigned_to',
            'assigned_to_name',
            ]
        
    def get_todo_type_display(self,obj):
        return obj.get_todo_type_display()
    
    def get_todo_status_display(self,obj):
        return obj.get_todo_status_display()
    
    def get_is_editable(self,obj):
        return (obj.todo_status != TodoListStatusEnum.COMPLETED and obj.assign == self.context['request'].user)
    
    def get_maternity(self,obj):
        if not obj.maternity_admission:
            return '-'
        
        name = obj.maternity_admission.maternity.name or '-'
        room_number = obj.maternity_admission.room.room_number or '-'
        return f"{name} - ({room_number})"
    
    def get_maternity_admission(self,obj):
        if not obj.maternity_admission:
            return None
        
        return obj.maternity_admission.aid
    
    def get_assign_name(self,obj):
        if not obj.assign:
            return '-'
        
        return obj.assign.name
    
    def get_assigned_to_name(self,obj):
        if not obj.assigned_to:
            return '-'
        
        return obj.assigned_to.name
    
    def get_assigned_to(self,obj):
        if not obj.assigned_to:
            return None
        
        return obj.assigned_to.sid
    
# 管理创建待办事项
class AssignTodoAssignCreateSerializer(serializers.ModelSerializer):
    
    assigned_to = serializers.CharField(required=False)
    maternity_admission = serializers.CharField(required=False)
    
    class Meta:
        model = TodoList
        fields = [
            'maternity_center',
            'assign',
            'assigned_to',
            'todo_type',
            'maternity_admission',
            'todo_content',
            'todo_remark',
            ]
    

    def validate_assigned_to(self,value):
        staff = Staff.get_staff_by_sid(sid=value,maternity_center=self.context['maternity_center'])
        if not staff:
            raise serializers.ValidationError("被指派人不存在")
        return staff
    
    def validate_maternity_admission(self,value):
        if not value:
            return None
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=value,maternity_center=self.context['maternity_center'],check_in_status=CheckInStatusEnum.CHECKED_IN)
        except MaternityAdmission.DoesNotExist:
            raise serializers.ValidationError("入院单不存在")
        
        return maternity_admission
        

# 管理更新待办事项
class AssignTodoAssignUpdateSerializer(serializers.ModelSerializer):
    
    assigned_to = serializers.CharField(required=False)
    maternity_admission = serializers.CharField(required=False)
    
    class Meta:
        model = TodoList
        fields = [
            'assigned_to',
            'maternity_admission',
            'todo_type',
            'todo_content',
            'todo_remark',
            ]


    def validate_assigned_to(self,value):
        staff = Staff.get_staff_by_sid(sid=value,maternity_center=self.context['maternity_center'])
        if not staff:
            raise serializers.ValidationError("被指派人不存在")
        return staff
    
    def validate_maternity_admission(self,value):
        if not value:
            return None
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=value,maternity_center=self.context['maternity_center'],check_in_status=CheckInStatusEnum.CHECKED_IN)
        except MaternityAdmission.DoesNotExist:
            raise serializers.ValidationError("入院单不存在")
        
        return maternity_admission



# 管理员工列表
class StaffSerializer(serializers.ModelSerializer):
    class Meta:
        model = Staff
        fields = ['sid','name']
        
        
class MaternitySelectListSerializer(serializers.ModelSerializer):
    
    maternity = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid','maternity']
    
    def get_maternity(self,obj):
        
        name = obj.maternity.name or '-'
        room_number = obj.room.room_number or '-'
        
        return f'{name}-({room_number})'